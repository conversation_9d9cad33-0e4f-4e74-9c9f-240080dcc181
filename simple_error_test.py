"""
Simple test to verify the error handling fixes work.
"""

import asyncio
import sys
import os

# Add the aganl directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'aganl'))

from perfect_contact_extractor import PerfectContactExtractor


async def test_simple():
    """Simple test with one good URL and one bad URL."""
    
    test_urls = [
        "https://commonplacecoffee.com/",      # This should work
        "https://www.bigdogcoffeeshop.com/"   # This was problematic
    ]
    
    print("🧪 SIMPLE ERROR HANDLING TEST")
    print("=" * 40)
    
    extractor = PerfectContactExtractor(batch_size=2, max_concurrent=1)
    results = await extractor.extract_perfect(test_urls)
    
    print(f"\n📊 Results ({len(results)} total):")
    for i, result in enumerate(results):
        print(f"\nResult {i+1}:")
        print(f"  URL: {result.get('url', 'Unknown')}")
        
        if 'error' in result:
            print(f"  Status: ❌ Error")
            print(f"  Error: {result['error']}")
        else:
            print(f"  Status: ✅ Success")
            email_data = result.get('email', {})
            if isinstance(email_data, dict):
                email = email_data.get('email', 'No email found')
            else:
                email = 'No email found'
            print(f"  Email: {email}")
            
            social_data = result.get('social_media', {})
            if isinstance(social_data, dict):
                social = social_data.get('platform', 'No social found')
            else:
                social = 'No social found'
            print(f"  Social: {social}")
    
    print("\n✅ Simple test completed!")


if __name__ == "__main__":
    try:
        asyncio.run(test_simple())
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
