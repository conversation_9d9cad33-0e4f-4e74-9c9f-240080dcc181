"""
Data collection commands for the CLI
"""
import typer
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.table import Table
from rich.text import Text
from typing import Optional, List
import questionary
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cli.categories import CategoryManager, CategoryGroup, CATEGORY_GROUPS
from cli.location import LocationService
from cli.collection_service import CollectionService

console = Console()

# Create collect app
collect_app = typer.Typer(help="Collect business data from various sources")


@collect_app.command("interactive")
def collect_interactive():
    """
    🎯 Interactive business data collection

    Guided process to collect business data with step-by-step prompts.
    """
    console.print(Panel(
        "Welcome to Interactive Business Data Collection! 🎯\n\n"
        "This wizard will guide you through collecting business data from multiple sources.",
        title="Interactive Collection",
        border_style="green"
    ))

    try:
        # Step 1: Category Selection
        console.print("\n[bold blue]Step 1: Select Business Categories[/bold blue]")
        selected_categories = _interactive_category_selection()

        if not selected_categories:
            console.print("[yellow]No categories selected. Exiting...[/yellow]")
            return

        # Step 2: Location Input
        console.print("\n[bold blue]Step 2: Set Search Location[/bold blue]")
        location = _interactive_location_input()

        if not location:
            console.print("[yellow]No location provided. Exiting...[/yellow]")
            return

        # Step 3: Radius Selection
        console.print("\n[bold blue]Step 3: Set Search Radius[/bold blue]")
        radius = _interactive_radius_selection()

        # Step 4: Data Source Selection
        console.print("\n[bold blue]Step 4: Select Data Sources[/bold blue]")
        sources = _interactive_source_selection()

        # Step 5: Output Configuration
        console.print("\n[bold blue]Step 5: Configure Output[/bold blue]")
        output_config = _interactive_output_configuration()

        # Step 6: Summary and Confirmation
        console.print("\n[bold blue]Step 6: Review and Confirm[/bold blue]")
        if _show_collection_summary(selected_categories, location, radius, sources, output_config):
            console.print("\n[green]Starting data collection...[/green]")

            # Execute collection
            collection_service = CollectionService()
            result = collection_service.collect_businesses(
                category_ids=selected_categories,
                location_data=location,
                radius_miles=radius,
                sources=sources,
                output_config=output_config
            )

            console.print(f"\n[bold green]Collection completed successfully![/bold green]")
            console.print(f"Collected {result['stats']['total_after_processing']} businesses")
        else:
            console.print("[yellow]Collection cancelled.[/yellow]")

    except KeyboardInterrupt:
        console.print("\n[yellow]Collection cancelled by user.[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error during interactive collection: {str(e)}[/red]")


def _interactive_category_selection() -> List[str]:
    """Interactive category selection with grouped display"""
    manager = CategoryManager()

    # First, let user choose selection method
    selection_method = questionary.select(
        "How would you like to select categories?",
        choices=[
            "Browse by category groups",
            "Search for specific categories",
            "Select from all categories"
        ]
    ).ask()

    if not selection_method:
        return []

    selected_categories = []

    if selection_method == "Browse by category groups":
        # Show category groups
        group_choices = [group.value for group in CategoryGroup]

        selected_groups = questionary.checkbox(
            "Select category groups to explore:",
            choices=group_choices
        ).ask()

        if not selected_groups:
            return []

        # For each selected group, show categories
        for group_name in selected_groups:
            group = next(g for g in CategoryGroup if g.value == group_name)
            categories = manager.get_categories_by_group(group)

            category_choices = [
                questionary.Choice(
                    title=f"{cat.name} - {cat.description}",
                    value=cat_id
                )
                for cat_id, cat in manager.get_all_categories().items()
                if cat in categories
            ]

            group_selections = questionary.checkbox(
                f"Select categories from {group_name}:",
                choices=category_choices
            ).ask()

            if group_selections:
                selected_categories.extend(group_selections)

    elif selection_method == "Search for specific categories":
        while True:
            search_query = questionary.text(
                "Enter search term (or press Enter to finish):"
            ).ask()

            if not search_query:
                break

            results = manager.search_categories(search_query)
            if not results:
                console.print(f"[yellow]No categories found for '{search_query}'[/yellow]")
                continue

            result_choices = [
                questionary.Choice(
                    title=f"{cat.name} - {cat.description}",
                    value=next(cat_id for cat_id, cat_data in manager.get_all_categories().items() if cat_data == cat)
                )
                for cat in results
            ]

            search_selections = questionary.checkbox(
                f"Select categories from search results:",
                choices=result_choices
            ).ask()

            if search_selections:
                selected_categories.extend(search_selections)

    else:  # Select from all categories
        all_categories = manager.get_all_categories()
        category_choices = [
            questionary.Choice(
                title=f"{cat.name} - {cat.description}",
                value=cat_id
            )
            for cat_id, cat in all_categories.items()
        ]

        selected_categories = questionary.checkbox(
            "Select categories:",
            choices=category_choices
        ).ask()

    # Remove duplicates
    selected_categories = list(set(selected_categories or []))

    if selected_categories:
        console.print(f"[green]Selected {len(selected_categories)} categories:[/green]")
        for cat_id in selected_categories:
            cat = manager.get_category(cat_id)
            if cat:
                console.print(f"  • {cat.name}")

    return selected_categories


def _interactive_location_input() -> Optional[dict]:
    """Interactive location input with validation"""
    location_service = LocationService()

    while True:
        location_input = questionary.text(
            "Enter location (city name, 'City, State', or coordinates 'lat,lng'):",
            validate=lambda x: len(x.strip()) > 0 or "Location cannot be empty"
        ).ask()

        if not location_input:
            return None

        # Parse the input
        parsed = location_service.parse_location_input(location_input)

        if parsed['type'] == 'invalid':
            console.print(f"[red]Error: {parsed['error']}[/red]")
            continue

        if parsed['type'] == 'coordinates':
            # Direct coordinates
            console.print(f"[green]Using coordinates: {parsed['latitude']}, {parsed['longitude']}[/green]")
            return {
                'type': 'coordinates',
                'latitude': parsed['latitude'],
                'longitude': parsed['longitude'],
                'name': f"{parsed['latitude']}, {parsed['longitude']}"
            }

        else:  # location_name
            # Try to geocode
            console.print(f"[yellow]Looking up location: {parsed['query']}...[/yellow]")

            location = location_service.geocode_location(parsed['query'])

            if not location:
                console.print(f"[red]Could not find location: {parsed['query']}[/red]")
                retry = questionary.confirm("Try a different location?").ask()
                if not retry:
                    return None
                continue

            # Show found location and confirm
            console.print(f"[green]Found location:[/green]")
            console.print(f"  Address: {location.formatted_address}")
            console.print(f"  Coordinates: {location.latitude}, {location.longitude}")

            confirm = questionary.confirm("Use this location?").ask()
            if confirm:
                return {
                    'type': 'geocoded',
                    'latitude': location.latitude,
                    'longitude': location.longitude,
                    'name': location.formatted_address,
                    'city': location.city,
                    'state': location.state,
                    'country': location.country
                }
            else:
                continue


def _interactive_radius_selection() -> float:
    """Interactive radius selection"""
    radius_options = [
        "1 mile",
        "5 miles",
        "10 miles",
        "15 miles",
        "25 miles",
        "50 miles",
        "Custom radius"
    ]

    radius_choice = questionary.select(
        "Select search radius:",
        choices=radius_options
    ).ask()

    if not radius_choice:
        return 10.0  # Default

    if radius_choice == "Custom radius":
        while True:
            try:
                custom_radius = questionary.text(
                    "Enter radius in miles:",
                    validate=lambda x: x.replace('.', '').isdigit() or "Please enter a valid number"
                ).ask()

                if not custom_radius:
                    return 10.0

                radius = float(custom_radius)
                if radius <= 0:
                    console.print("[red]Radius must be greater than 0[/red]")
                    continue
                if radius > 100:
                    console.print("[yellow]Warning: Large radius may result in many results and longer processing time[/yellow]")

                return radius
            except ValueError:
                console.print("[red]Please enter a valid number[/red]")
    else:
        # Extract number from choice
        return float(radius_choice.split()[0])


def _interactive_source_selection() -> List[str]:
    """Interactive data source selection"""
    source_options = [
        questionary.Choice("Google Places API (most reliable, requires API key)", "google"),
        questionary.Choice("Overpass API (free OpenStreetMap data)", "overpass"),
        questionary.Choice("Yellow Pages (web scraping)", "yellowpages")
    ]

    selected_sources = questionary.checkbox(
        "Select data sources to use:",
        choices=source_options
    ).ask()

    if not selected_sources:
        # Default to all sources
        return ["google", "overpass", "yellowpages"]

    return selected_sources


def _interactive_output_configuration() -> dict:
    """Interactive output configuration"""
    output_format = questionary.select(
        "Select output format:",
        choices=["CSV", "JSON", "Both CSV and JSON"]
    ).ask()

    custom_filename = questionary.confirm(
        "Use custom filename?"
    ).ask()

    filename = None
    if custom_filename:
        filename = questionary.text(
            "Enter filename (without extension):",
            validate=lambda x: len(x.strip()) > 0 or "Filename cannot be empty"
        ).ask()

    return {
        'format': output_format or "CSV",
        'custom_filename': filename
    }


def _show_collection_summary(categories: List[str], location: dict, radius: float,
                           sources: List[str], output_config: dict) -> bool:
    """Show collection summary and get confirmation"""
    manager = CategoryManager()

    # Create summary table
    table = Table(title="Collection Summary", show_header=True, header_style="bold blue")
    table.add_column("Setting", style="cyan", no_wrap=True)
    table.add_column("Value", style="white")

    # Categories
    category_names = []
    for cat_id in categories:
        cat = manager.get_category(cat_id)
        if cat:
            category_names.append(cat.name)

    table.add_row("Categories", ", ".join(category_names))
    table.add_row("Location", location['name'])
    table.add_row("Coordinates", f"{location['latitude']}, {location['longitude']}")
    table.add_row("Radius", f"{radius} miles")
    table.add_row("Data Sources", ", ".join(sources))
    table.add_row("Output Format", output_config['format'])

    if output_config['custom_filename']:
        table.add_row("Filename", output_config['custom_filename'])

    console.print(table)

    return questionary.confirm(
        "\nProceed with data collection?"
    ).ask() or False


@collect_app.command("quick")
def collect_quick(
    categories: Optional[List[str]] = typer.Option(
        None, "--category", "-c", help="Business categories to search for"
    ),
    location: Optional[str] = typer.Option(
        None, "--location", "-l", help="City or location to search in"
    ),
    radius: Optional[float] = typer.Option(
        10.0, "--radius", "-r", help="Search radius in miles"
    ),
    sources: Optional[List[str]] = typer.Option(
        ["google", "overpass", "yellowpages"],
        "--source", "-s",
        help="Data sources to use (google, overpass, yellowpages)"
    ),
    output: Optional[str] = typer.Option(
        None, "--output", "-o", help="Output file path"
    ),
    amount: Optional[int] = typer.Option(
        None, "--amount", "-a", help="Limit number of results per category (for testing)"
    )
):
    """
    ⚡ Quick business data collection with command-line arguments
    """
    if not categories:
        console.print("[red]Error: At least one category is required[/red]")
        console.print("[yellow]Use 'categories list' to see available categories[/yellow]")
        raise typer.Exit(1)

    if not location:
        console.print("[red]Error: Location is required[/red]")
        raise typer.Exit(1)

    # Validate categories
    manager = CategoryManager()
    valid_categories = []
    invalid_categories = []

    for category in categories:
        # Try exact match first
        cat = manager.get_category(category.lower().replace(" ", "_"))
        if cat:
            valid_categories.append(category.lower().replace(" ", "_"))
        else:
            # Try search
            search_results = manager.search_categories(category)
            if search_results:
                # Use first match
                cat_id = next(cat_id for cat_id, cat_data in manager.get_all_categories().items()
                             if cat_data == search_results[0])
                valid_categories.append(cat_id)
                console.print(f"[yellow]Matched '{category}' to '{search_results[0].name}'[/yellow]")
            else:
                invalid_categories.append(category)

    if invalid_categories:
        console.print(f"[red]Invalid categories: {', '.join(invalid_categories)}[/red]")
        console.print("[yellow]Use 'categories search <term>' to find valid categories[/yellow]")
        raise typer.Exit(1)

    # Validate location
    location_service = LocationService()
    parsed_location = location_service.parse_location_input(location)

    if parsed_location['type'] == 'invalid':
        console.print(f"[red]Invalid location: {parsed_location['error']}[/red]")
        raise typer.Exit(1)

    location_data = None
    if parsed_location['type'] == 'coordinates':
        location_data = {
            'type': 'coordinates',
            'latitude': parsed_location['latitude'],
            'longitude': parsed_location['longitude'],
            'name': f"{parsed_location['latitude']}, {parsed_location['longitude']}"
        }
    else:
        console.print(f"[yellow]Looking up location: {location}...[/yellow]")
        geocoded = location_service.geocode_location(location)
        if not geocoded:
            console.print(f"[red]Could not find location: {location}[/red]")
            raise typer.Exit(1)

        location_data = {
            'type': 'geocoded',
            'latitude': geocoded.latitude,
            'longitude': geocoded.longitude,
            'name': geocoded.formatted_address
        }

    # Validate sources
    valid_sources = ["google", "overpass", "yellowpages"]
    invalid_sources = [s for s in sources if s not in valid_sources]
    if invalid_sources:
        console.print(f"[red]Invalid sources: {', '.join(invalid_sources)}[/red]")
        console.print(f"[yellow]Valid sources: {', '.join(valid_sources)}[/yellow]")
        raise typer.Exit(1)

    # Show summary
    console.print(Panel(
        f"[bold]Quick Collection Summary[/bold]\n\n"
        f"Categories: {', '.join([manager.get_category(c).name for c in valid_categories])}\n"
        f"Location: {location_data['name']}\n"
        f"Coordinates: {location_data['latitude']}, {location_data['longitude']}\n"
        f"Radius: {radius} miles\n"
        f"Sources: {', '.join(sources)}\n"
        f"Output: {output or 'Default filename'}",
        title="Collection Configuration",
        border_style="green"
    ))

    console.print("\n[green]Starting data collection...[/green]")

    # Execute collection
    collection_service = CollectionService()
    result = collection_service.collect_businesses(
        category_ids=valid_categories,
        location_data=location_data,
        radius_miles=radius,
        sources=sources,
        output_config={'format': 'CSV', 'custom_filename': output},
        max_results_per_category=amount
    )

    console.print(f"\n[bold green]Quick collection completed successfully![/bold green]")
    console.print(f"Collected {result['stats']['total_after_processing']} businesses")


@collect_app.callback()
def collect_callback():
    """Business data collection commands"""
    pass
