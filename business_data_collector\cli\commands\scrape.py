"""
Email scraping commands for the CLI
"""
import typer
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.table import Table
from typing import Optional, List
import pandas as pd
import asyncio
import sys
import os
from datetime import datetime

# Add paths for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
aganl_path = os.path.join(project_root, 'aganl')

sys.path.append(project_root)
sys.path.append(aganl_path)

try:
    from perfect_contact_extractor import PerfectContactExtractor
    SCRAPING_AVAILABLE = True
except ImportError as e:
    console = Console()
    console.print(f"[yellow]Warning: Could not import PerfectContactExtractor: {e}[/yellow]")
    console.print(f"[yellow]Scraping functionality will be limited. Make sure aganl module is available at: {aganl_path}[/yellow]")
    PerfectContactExtractor = None
    SCRAPING_AVAILABLE = False

console = Console()

# Create scrape app
scrape_app = typer.Typer(help="Scrape emails and contact info from businesses")


@scrape_app.command("emails")
def scrape_emails(
    input_file: str = typer.Argument(..., help="CSV file with business data"),
    output_file: Optional[str] = typer.Option(
        None, "--output", "-o", help="Output file for scraped emails"
    ),
    batch_size: int = typer.Option(
        25, "--batch-size", "-b", help="Number of URLs to process per batch"
    ),
    max_concurrent: int = typer.Option(
        5, "--concurrent", "-c", help="Maximum concurrent requests per batch"
    ),
    website_column: str = typer.Option(
        "website", "--website-col", help="Column name containing website URLs"
    ),
    amount: Optional[int] = typer.Option(
        None, "--amount", "-a", help="Limit number of URLs to process (for testing)"
    ),
    limit: Optional[int] = typer.Option(
        None, "--limit", "-l", help="Limit number of URLs to process (for testing)"
    )
):
    """
    📧 Scrape emails from business websites
    """
    if not SCRAPING_AVAILABLE:
        console.print("[red]Error: Email scraping functionality not available[/red]")
        console.print("[yellow]Make sure the aganl module is properly installed[/yellow]")
        raise typer.Exit(1)

    console.print(Panel(
        f"Scraping emails from businesses in: {input_file}\n"
        f"Batch size: {batch_size}, Concurrent: {max_concurrent}",
        title="Email Scraping",
        border_style="cyan"
    ))

    # Run the async scraping
    asyncio.run(_scrape_emails_async(input_file, output_file, batch_size, max_concurrent, website_column, amount))


@scrape_app.command("contacts")
def scrape_contacts(
    input_file: str = typer.Argument(..., help="CSV file with business data"),
    output_file: Optional[str] = typer.Option(
        None, "--output", "-o", help="Output file for scraped contacts"
    ),
    batch_size: int = typer.Option(
        25, "--batch-size", "-b", help="Number of URLs to process per batch"
    ),
    max_concurrent: int = typer.Option(
        5, "--concurrent", "-c", help="Maximum concurrent requests per batch"
    ),
    website_column: str = typer.Option(
        "website", "--website-col", help="Column name containing website URLs"
    ),
    amount: Optional[int] = typer.Option(
        None, "--amount", "-a", help="Limit number of URLs to process (for testing)"
    )
):
    """
    📞 Scrape all contact information from business websites
    """
    if not SCRAPING_AVAILABLE:
        console.print("[red]Error: Contact scraping functionality not available[/red]")
        console.print("[yellow]Make sure the aganl module is properly installed[/yellow]")
        raise typer.Exit(1)

    console.print(Panel(
        f"Scraping contacts from businesses in: {input_file}\n"
        f"Batch size: {batch_size}, Concurrent: {max_concurrent}",
        title="Contact Scraping",
        border_style="cyan"
    ))

    # Run the async scraping
    asyncio.run(_scrape_contacts_async(input_file, output_file, batch_size, max_concurrent, website_column, amount))


async def _scrape_emails_async(input_file: str, output_file: Optional[str],
                              batch_size: int, max_concurrent: int, website_column: str, amount: Optional[int] = None):
    """Async function to scrape emails from business websites"""
    try:
        # Load business data
        console.print("[blue]Loading business data...[/blue]")

        if not os.path.exists(input_file):
            console.print(f"[red]Error: Input file '{input_file}' not found[/red]")
            return

        df = pd.read_csv(input_file)

        if website_column not in df.columns:
            console.print(f"[red]Error: Column '{website_column}' not found in CSV[/red]")
            console.print(f"[yellow]Available columns: {', '.join(df.columns)}[/yellow]")
            return

        # Filter out rows without websites
        df_with_websites = df[df[website_column].notna() & (df[website_column] != '')]

        if len(df_with_websites) == 0:
            console.print("[yellow]No businesses with websites found in the data[/yellow]")
            return

        console.print(f"[green]Found {len(df_with_websites)} businesses with websites[/green]")

        # Limit the number of URLs if amount is specified
        if amount and amount < len(df_with_websites):
            df_with_websites = df_with_websites.head(amount)
            console.print(f"[yellow]Limiting to first {amount} businesses for processing[/yellow]")

        # Extract URLs
        urls = df_with_websites[website_column].tolist()

        # Initialize extractor
        extractor = PerfectContactExtractor(batch_size=batch_size, max_concurrent=max_concurrent)

        # Scrape emails
        console.print(f"[blue]Starting email extraction from {len(urls)} websites...[/blue]")
        results = await extractor.extract_perfect(urls)

        # Process results
        email_results = []
        for i, result in enumerate(results):
            business_data = df_with_websites.iloc[i].to_dict()

            if 'error' not in result:
                email_info = result.get('email')
                if email_info:
                    email_results.append({
                        **business_data,
                        'scraped_email': email_info.get('email', ''),
                        'email_confidence': email_info.get('confidence', 0),
                        'pages_checked': result.get('pages_checked', 0),
                        'scrape_timestamp': datetime.now().isoformat()
                    })
                else:
                    email_results.append({
                        **business_data,
                        'scraped_email': '',
                        'email_confidence': 0,
                        'pages_checked': result.get('pages_checked', 0),
                        'scrape_timestamp': datetime.now().isoformat()
                    })
            else:
                email_results.append({
                    **business_data,
                    'scraped_email': '',
                    'email_confidence': 0,
                    'pages_checked': 0,
                    'scrape_error': result.get('error', ''),
                    'scrape_timestamp': datetime.now().isoformat()
                })

        # Save results
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"scraped_emails_{timestamp}.csv"

        results_df = pd.DataFrame(email_results)
        results_df.to_csv(output_file, index=False)

        # Show summary
        emails_found = len([r for r in email_results if r.get('scraped_email')])

        console.print(Panel(
            f"[bold]Email Scraping Complete![/bold]\n\n"
            f"• Total businesses processed: {len(results)}\n"
            f"• Emails found: {emails_found}\n"
            f"• Success rate: {emails_found/len(results)*100:.1f}%\n"
            f"• Results saved to: {output_file}",
            title="Scraping Summary",
            border_style="green"
        ))

    except Exception as e:
        console.print(f"[red]Error during email scraping: {str(e)}[/red]")


async def _scrape_contacts_async(input_file: str, output_file: Optional[str],
                                batch_size: int, max_concurrent: int, website_column: str, amount: Optional[int] = None):
    """Async function to scrape all contacts from business websites"""
    try:
        # Load business data
        console.print("[blue]Loading business data...[/blue]")

        if not os.path.exists(input_file):
            console.print(f"[red]Error: Input file '{input_file}' not found[/red]")
            return

        df = pd.read_csv(input_file)

        if website_column not in df.columns:
            console.print(f"[red]Error: Column '{website_column}' not found in CSV[/red]")
            console.print(f"[yellow]Available columns: {', '.join(df.columns)}[/yellow]")
            return

        # Filter out rows without websites
        df_with_websites = df[df[website_column].notna() & (df[website_column] != '')]

        if len(df_with_websites) == 0:
            console.print("[yellow]No businesses with websites found in the data[/yellow]")
            return

        console.print(f"[green]Found {len(df_with_websites)} businesses with websites[/green]")

        # Limit the number of URLs if amount is specified
        if amount and amount < len(df_with_websites):
            df_with_websites = df_with_websites.head(amount)
            console.print(f"[yellow]Limiting to first {amount} businesses for processing[/yellow]")

        # Extract URLs
        urls = df_with_websites[website_column].tolist()

        # Initialize extractor
        extractor = PerfectContactExtractor(batch_size=batch_size, max_concurrent=max_concurrent)

        # Scrape contacts
        console.print(f"[blue]Starting contact extraction from {len(urls)} websites...[/blue]")
        results = await extractor.extract_perfect(urls)

        # Process results
        contact_results = []
        for i, result in enumerate(results):
            business_data = df_with_websites.iloc[i].to_dict()

            if 'error' not in result:
                email_info = result.get('email')
                social_info = result.get('social_media')

                contact_results.append({
                    **business_data,
                    'scraped_email': email_info.get('email', '') if email_info else '',
                    'email_confidence': email_info.get('confidence', 0) if email_info else 0,
                    'social_platform': social_info.get('platform', '') if social_info else '',
                    'social_handle': social_info.get('handle', '') if social_info else '',
                    'social_url': social_info.get('url', '') if social_info else '',
                    'pages_checked': result.get('pages_checked', 0),
                    'scrape_timestamp': datetime.now().isoformat()
                })
            else:
                contact_results.append({
                    **business_data,
                    'scraped_email': '',
                    'email_confidence': 0,
                    'social_platform': '',
                    'social_handle': '',
                    'social_url': '',
                    'pages_checked': 0,
                    'scrape_error': result.get('error', ''),
                    'scrape_timestamp': datetime.now().isoformat()
                })

        # Save results
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"scraped_contacts_{timestamp}.csv"

        results_df = pd.DataFrame(contact_results)
        results_df.to_csv(output_file, index=False)

        # Show summary
        emails_found = len([r for r in contact_results if r.get('scraped_email')])
        socials_found = len([r for r in contact_results if r.get('social_platform')])

        console.print(Panel(
            f"[bold]Contact Scraping Complete![/bold]\n\n"
            f"• Total businesses processed: {len(results)}\n"
            f"• Emails found: {emails_found}\n"
            f"• Social media found: {socials_found}\n"
            f"• Email success rate: {emails_found/len(results)*100:.1f}%\n"
            f"• Social success rate: {socials_found/len(results)*100:.1f}%\n"
            f"• Results saved to: {output_file}",
            title="Scraping Summary",
            border_style="green"
        ))

    except Exception as e:
        console.print(f"[red]Error during contact scraping: {str(e)}[/red]")


@scrape_app.callback()
def scrape_callback():
    """Email and contact scraping commands"""
    pass
