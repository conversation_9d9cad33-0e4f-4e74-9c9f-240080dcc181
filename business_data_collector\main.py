"""
Main coordinator script for business data collection.
"""
import logging
import sys
import os
from datetime import datetime
from typing import List, Dict, Any

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from collectors.google_places import GooglePlacesCollector
from collectors.overpass_api import OverpassAPICollector
from collectors.yellow_pages import YellowPagesCollector
from utils.deduplication import BusinessDeduplicator
from utils.filtering import BusinessFilter
from utils.data_export import DataExporter
from config import LOG_LEVEL, LOG_FORMAT, OUTPUT_FILENAME, OPENAI_API_KEY


def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL),
        format=LOG_FORMAT,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('business_collector.log')
        ]
    )


def collect_from_google_places() -> List[Dict[str, Any]]:
    """Collect business data from Google Places API."""
    logger = logging.getLogger(__name__)
    logger.info("Starting Google Places API collection...")
    
    try:
        collector = GooglePlacesCollector()
        businesses = collector.collect_businesses()
        logger.info(f"Google Places API: Collected {len(businesses)} businesses")
        return businesses
    except Exception as e:
        logger.error(f"Error collecting from Google Places API: {str(e)}")
        return []


def collect_from_overpass_api() -> List[Dict[str, Any]]:
    """Collect business data from Overpass API."""
    logger = logging.getLogger(__name__)
    logger.info("Starting Overpass API collection...")
    
    try:
        collector = OverpassAPICollector()
        businesses = collector.collect_businesses()
        logger.info(f"Overpass API: Collected {len(businesses)} businesses")
        return businesses
    except Exception as e:
        logger.error(f"Error collecting from Overpass API: {str(e)}")
        return []


def collect_from_yellow_pages() -> List[Dict[str, Any]]:
    """Collect business data from Yellow Pages."""
    logger = logging.getLogger(__name__)
    logger.info("Starting Yellow Pages collection...")

    try:
        collector = YellowPagesCollector(
            llm_provider="openai/gpt-4o-mini",
            api_key=OPENAI_API_KEY
        )
        businesses = collector.collect_businesses()
        logger.info(f"Yellow Pages: Collected {len(businesses)} businesses")
        return businesses
    except Exception as e:
        logger.error(f"Error collecting from Yellow Pages: {str(e)}")
        return []


def process_businesses(businesses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Process collected business data (filter and deduplicate)."""
    logger = logging.getLogger(__name__)
    logger.info(f"Starting processing of {len(businesses)} businesses...")
    
    if not businesses:
        logger.warning("No businesses to process")
        return []
    
    # Add collection timestamp
    timestamp = datetime.now().isoformat()
    for business in businesses:
        business['collected_at'] = timestamp
    
    # Filter out corporate chains and invalid businesses
    logger.info("Filtering businesses...")
    business_filter = BusinessFilter()
    filtered_businesses = business_filter.filter_businesses(businesses)
    
    # Deduplicate businesses
    logger.info("Deduplicating businesses...")
    deduplicator = BusinessDeduplicator()
    unique_businesses = deduplicator.deduplicate_businesses(filtered_businesses)
    
    logger.info(f"Processing complete: {len(unique_businesses)} unique businesses")
    return unique_businesses


def export_results(businesses: List[Dict[str, Any]]) -> None:
    """Export processed business data to files."""
    logger = logging.getLogger(__name__)
    logger.info("Exporting results...")
    
    if not businesses:
        logger.warning("No businesses to export")
        return
    
    exporter = DataExporter()
    
    # Export to CSV
    csv_path = exporter.export_to_csv(businesses, OUTPUT_FILENAME)
    if csv_path:
        logger.info(f"CSV exported to: {csv_path}")
    
    # Export to JSON
    json_path = exporter.export_to_json(businesses)
    if json_path:
        logger.info(f"JSON exported to: {json_path}")
    
    # Export summary report
    report_path = exporter.export_summary_report(businesses)
    if report_path:
        logger.info(f"Summary report exported to: {report_path}")


def main():
    """Main function to orchestrate the data collection process."""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info("BUSINESS DATA COLLECTION STARTED")
    logger.info("=" * 60)
    
    start_time = datetime.now()
    all_businesses = []
    
    # Collect from all sources
    try:
        # Google Places API (most reliable)
        google_businesses = collect_from_google_places()
        all_businesses.extend(google_businesses)
        
        # Overpass API (free, good coverage)
        overpass_businesses = collect_from_overpass_api()
        all_businesses.extend(overpass_businesses)
        
        # Yellow Pages (additional coverage)
        yellow_pages_businesses = collect_from_yellow_pages()
        all_businesses.extend(yellow_pages_businesses)
        
        logger.info(f"Total businesses collected from all sources: {len(all_businesses)}")
        
        # Process the collected data
        processed_businesses = process_businesses(all_businesses)
        
        # Export results
        export_results(processed_businesses)
        
        # Final summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("=" * 60)
        logger.info("BUSINESS DATA COLLECTION COMPLETED")
        logger.info("=" * 60)
        logger.info(f"Total runtime: {duration}")
        logger.info(f"Raw businesses collected: {len(all_businesses)}")
        logger.info(f"Final processed businesses: {len(processed_businesses)}")
        
        # Source breakdown
        sources = {}
        for business in processed_businesses:
            source = business.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        
        logger.info("Final breakdown by source:")
        for source, count in sorted(sources.items()):
            logger.info(f"  {source}: {count} businesses")
        
        print(f"\n✅ Collection completed successfully!")
        print(f"📊 Collected {len(processed_businesses)} unique local businesses")
        print(f"📁 Results saved to: output/{OUTPUT_FILENAME}")
        print(f"⏱️  Total runtime: {duration}")
        
    except KeyboardInterrupt:
        logger.info("Collection interrupted by user")
        print("\n❌ Collection interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error during collection: {str(e)}")
        print(f"\n❌ Error during collection: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
