"""
Temporary script to extract emails from Pittsburgh coffee shops using crawl4ai's AI extract mode with Groq.
This script uses LLM-based extraction for comparison with the CSS/regex approach.
"""

import asyncio
import csv
import sys
import os
import json
from datetime import datetime
from typing import List, Dict, Optional
from pydantic import BaseModel, Field

# Add the business_data_collector directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'business_data_collector'))

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, LLMConfig
from crawl4ai import LLMExtractionStrategy
from config import GROQ_API_KEY


class ContactInfo(BaseModel):
    """Pydantic model for structured contact information extraction"""
    email: Optional[str] = Field(None, description="Primary email address found on the page")
    email_confidence: Optional[float] = Field(None, description="Confidence score for email (0.0-1.0)")
    social_platform: Optional[str] = Field(None, description="Social media platform (instagram, facebook, twitter)")
    social_handle: Optional[str] = Field(None, description="Social media handle or username")
    social_url: Optional[str] = Field(None, description="Full social media URL")
    social_confidence: Optional[float] = Field(None, description="Confidence score for social media (0.0-1.0)")
    additional_emails: Optional[List[str]] = Field(default_factory=list, description="Any additional email addresses found")
    phone_number: Optional[str] = Field(None, description="Phone number if clearly visible")


class AIContactExtractor:
    """
    AI-powered contact extractor using Groq's Llama model via crawl4ai's LLM extraction.
    """
    
    def __init__(self, batch_size: int = 20, max_concurrent: int = 3):
        """Initialize the AI contact extractor."""
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.groq_api_key = GROQ_API_KEY
        
        if not self.groq_api_key:
            raise ValueError("GROQ_API_KEY not found in environment variables")
    
    def _get_llm_extraction_strategy(self) -> LLMExtractionStrategy:
        """Create the LLM extraction strategy for contact information."""

        # Detailed instruction for the AI model
        instruction = """
        You are an expert at extracting contact information from business websites.

        Analyze the webpage content and extract contact information. Return a JSON object with these exact fields:

        {
          "email": "primary email address or null",
          "email_confidence": 0.8,
          "social_platform": "instagram/facebook/twitter or null",
          "social_handle": "@username or null",
          "social_url": "full URL or null",
          "social_confidence": 0.8,
          "additional_emails": ["email1", "email2"],
          "phone_number": "phone number or null"
        }

        RULES:
        1. EMAIL: Look for contact@, info@, hello@, admin@ - avoid noreply@, support@
        2. SOCIAL: Prioritize Instagram, then Facebook, then Twitter/X
        3. CONFIDENCE: 0.9-1.0 (contact section), 0.7-0.9 (header/footer), 0.5-0.7 (content), 0.3-0.5 (uncertain)
        4. Use null for missing values, not empty strings
        5. Return ONLY valid JSON, no extra text

        If no contact info found, return: {"email": null, "email_confidence": null, "social_platform": null, "social_handle": null, "social_url": null, "social_confidence": null, "additional_emails": [], "phone_number": null}
        """
        
        # Configure Groq LLM
        llm_config = LLMConfig(
            provider="groq/llama-3.1-8b-instant",  # Fast and efficient Groq model
            api_token=self.groq_api_key
        )
        
        # Create LLM extraction strategy
        return LLMExtractionStrategy(
            llm_config=llm_config,
            schema=ContactInfo.model_json_schema(),
            extraction_type="schema",
            instruction=instruction,
            chunk_token_threshold=3000,  # Larger chunks for better context
            overlap_rate=0.0,  # No overlap to avoid confusion
            apply_chunking=False,  # Disable chunking for simpler processing
            input_format="markdown",  # Use markdown for cleaner input
            extra_args={
                "temperature": 0.0,  # Very low temperature for consistent JSON
                "max_tokens": 500,  # Smaller response for just contact info
                "top_p": 0.95
            },
            verbose=True
        )
    
    async def extract_contacts_ai(self, urls: List[str]) -> List[Dict]:
        """
        Extract contact information using AI-powered analysis.
        
        Args:
            urls: List of URLs to process
            
        Returns:
            List of extraction results
        """
        if not urls:
            return []
        
        print(f"🤖 AI CONTACT EXTRACTION WITH GROQ")
        print(f"📊 Processing {len(urls)} URLs with AI-powered analysis")
        print(f"⚡ Max concurrent per batch: {self.max_concurrent}")
        print(f"🧠 Model: groq/llama-3.1-8b-instant")
        
        all_results = []
        total_start = datetime.now()
        
        # Process URLs in batches
        for i in range(0, len(urls), self.batch_size):
            batch = urls[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (len(urls) + self.batch_size - 1) // self.batch_size
            
            print(f"\n📦 Batch {batch_num}/{total_batches}: {len(batch)} URLs")
            
            batch_start = datetime.now()
            batch_results = await self._process_batch_ai(batch)
            batch_duration = (datetime.now() - batch_start).total_seconds()
            
            all_results.extend(batch_results)
            
            print(f"   ✅ Completed in {batch_duration:.2f}s ({len(batch)/batch_duration:.2f} URLs/sec)")
            
            # Small delay between batches to be respectful
            if i + self.batch_size < len(urls):
                await asyncio.sleep(2)
        
        total_duration = (datetime.now() - total_start).total_seconds()
        
        print(f"\n🎉 AI EXTRACTION COMPLETED!")
        print(f"   • Total URLs: {len(urls)}")
        print(f"   • Total time: {total_duration:.2f}s ({total_duration/60:.1f} min)")
        print(f"   • Overall rate: {len(urls)/total_duration:.2f} URLs/second")
        
        return all_results
    
    async def _process_batch_ai(self, urls: List[str]) -> List[Dict]:
        """Process a batch of URLs with AI extraction."""
        
        # Create browser and crawler configs
        browser_config = BrowserConfig(
            headless=True,
            verbose=False,  # Reduce noise
            text_mode=True,  # Faster loading for text extraction
            viewport_width=1280,
            viewport_height=720
        )
        
        llm_strategy = self._get_llm_extraction_strategy()
        
        crawler_config = CrawlerRunConfig(
            extraction_strategy=llm_strategy,
            cache_mode=CacheMode.BYPASS,  # Always fresh for comparison
            word_count_threshold=50,  # Lower threshold for contact pages
            verbose=False  # Reduce noise
        )
        
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_single(url: str) -> Dict:
            async with semaphore:
                return await self._extract_single_ai(url, browser_config, crawler_config, llm_strategy)
        
        tasks = [process_single(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append({
                    "url": urls[i],
                    "error": str(result),
                    "timestamp": datetime.now().isoformat(),
                    "extraction_method": "ai_groq"
                })
            else:
                final_results.append(result)
        
        return final_results
    
    async def _extract_single_ai(self, url: str, browser_config: BrowserConfig, 
                                crawler_config: CrawlerRunConfig, llm_strategy: LLMExtractionStrategy) -> Dict:
        """Extract from single URL using AI."""
        
        try:
            domain = url.split('/')[2].replace('www.', '')
            print(f"   🤖 {domain}: AI analyzing page content...")
            
            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await asyncio.wait_for(
                    crawler.arun(url=url, config=crawler_config),
                    timeout=30.0  # Reasonable timeout for AI processing
                )
                
                if result.success and result.extracted_content:
                    try:
                        # Debug: Print raw response
                        print(f"     🔍 {domain}: Raw AI response: {result.extracted_content[:200]}...")

                        # Parse the AI-extracted JSON
                        contact_data = json.loads(result.extracted_content)

                        # Handle both single object and array responses
                        if isinstance(contact_data, list) and len(contact_data) > 0:
                            contact_data = contact_data[0]  # Take first item if array

                        # Validate and clean the data
                        if isinstance(contact_data, dict):
                            print(f"     ✅ {domain}: Successfully parsed AI response")
                            return {
                                "url": url,
                                "timestamp": datetime.now().isoformat(),
                                "extraction_method": "ai_groq",
                                "email": contact_data.get("email"),
                                "email_confidence": contact_data.get("email_confidence"),
                                "social_platform": contact_data.get("social_platform"),
                                "social_handle": contact_data.get("social_handle"),
                                "social_url": contact_data.get("social_url"),
                                "social_confidence": contact_data.get("social_confidence"),
                                "additional_emails": contact_data.get("additional_emails", []),
                                "phone_number": contact_data.get("phone_number"),
                                "success": True,
                                "ai_tokens_used": getattr(llm_strategy, 'total_usage', {}).get('total_tokens', 0)
                            }
                        else:
                            print(f"     ⚠️ {domain}: Unexpected AI response format: {type(contact_data)}")
                            return self._create_error_result(url, f"Invalid AI response format: {type(contact_data)}")

                    except json.JSONDecodeError as e:
                        print(f"     ❌ {domain}: AI response parsing failed - {str(e)}")
                        print(f"     📄 Raw response: {result.extracted_content}")
                        return self._create_error_result(url, f"JSON parsing error: {str(e)}")
                else:
                    error_msg = result.error_message if hasattr(result, 'error_message') else "Unknown crawl error"
                    print(f"     ❌ {domain}: Crawl failed - {error_msg}")
                    return self._create_error_result(url, error_msg)
        
        except asyncio.TimeoutError:
            print(f"     ⏰ {domain}: AI processing timeout")
            return self._create_error_result(url, "AI processing timeout")
        except Exception as e:
            print(f"     ❌ {domain}: Unexpected error - {str(e)}")
            return self._create_error_result(url, str(e))
    
    def _create_error_result(self, url: str, error: str) -> Dict:
        """Create a standardized error result."""
        return {
            "url": url,
            "error": error,
            "timestamp": datetime.now().isoformat(),
            "extraction_method": "ai_groq"
        }
    
    def export_to_csv(self, results: List[Dict], filename: str):
        """Export AI extraction results to CSV."""
        if not results:
            return
        
        rows = []
        for result in results:
            if 'error' in result:
                continue
            
            row = {
                'url': result.get('url', ''),
                'email': result.get('email', ''),
                'email_confidence': result.get('email_confidence', ''),
                'social_platform': result.get('social_platform', ''),
                'social_handle': result.get('social_handle', ''),
                'social_url': result.get('social_url', ''),
                'social_confidence': result.get('social_confidence', ''),
                'phone_number': result.get('phone_number', ''),
                'additional_emails': ', '.join(result.get('additional_emails', [])),
                'ai_tokens_used': result.get('ai_tokens_used', 0),
                'extraction_method': result.get('extraction_method', 'ai_groq'),
                'timestamp': result.get('timestamp', '')
            }
            rows.append(row)
        
        if rows:
            fieldnames = list(rows[0].keys())
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(rows)
            print(f"📊 AI results exported to {filename}")
    
    def print_summary(self, results: List[Dict]):
        """Print AI extraction summary."""
        total = len(results)
        successful = len([r for r in results if 'error' not in r])
        errors = total - successful
        
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_platform')])
        phones_found = len([r for r in results if 'error' not in r and r.get('phone_number')])
        
        total_tokens = sum(r.get('ai_tokens_used', 0) for r in results if 'error' not in r)
        
        print(f"\n📊 AI EXTRACTION SUMMARY:")
        print(f"   • Total URLs: {total}")
        print(f"   • Successful: {successful}")
        print(f"   • Errors: {errors}")
        
        if successful > 0:
            print(f"   • Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
            print(f"   • Social media found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
            print(f"   • Phone numbers found: {phones_found}/{successful} ({phones_found/successful*100:.1f}%)")
            print(f"   • Both email & social: {len([r for r in results if 'error' not in r and r.get('email') and r.get('social_platform')])}/{successful}")
            print(f"   • Total AI tokens used: {total_tokens:,}")
            print(f"   • Average tokens per URL: {total_tokens/successful:.1f}")


def load_urls_from_pgh_csv(filename: str) -> List[str]:
    """Load URLs from the Pittsburgh shops CSV file."""
    urls = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                website = row.get('website', '').strip()
                if website and website.startswith(('http://', 'https://')):
                    urls.append(website)
        
        print(f"✅ Loaded {len(urls)} valid website URLs from {filename}")
        return urls
        
    except Exception as e:
        print(f"❌ Error loading URLs from {filename}: {e}")
        return []


async def main():
    """Main function to run AI-powered email extraction."""
    print("🤖 PITTSBURGH COFFEE SHOPS AI EMAIL EXTRACTION")
    print("=" * 65)
    print("🧠 Using Groq's Llama-3.1-8B-Instant for AI-powered contact extraction")
    
    # Load URLs from the CSV file
    csv_filename = "pghshopsfromapi.csv"
    urls = load_urls_from_pgh_csv(csv_filename)
    
    if not urls:
        print("❌ No valid URLs found in the CSV file.")
        return
    
    print(f"\n🎯 Processing {len(urls)} Pittsburgh coffee shop websites")
    print(f"⚙️  Using AI extraction with Groq API")
    
    # Initialize the AI extractor
    extractor = AIContactExtractor(
        batch_size=15,  # Smaller batches for AI processing
        max_concurrent=3  # Conservative concurrency for AI calls
    )
    
    # Start extraction
    start_time = datetime.now()
    print(f"\n🚀 Starting AI extraction at {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        results = await extractor.extract_contacts_ai(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        print(f"\n✅ AI extraction completed in {duration:.2f} seconds")
        print(f"⚡ Processing rate: {len(urls)/duration:.2f} URLs/second")
        
        # Print comprehensive summary
        extractor.print_summary(results)
        
        # Export results with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"pittsburgh_ai_contacts_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        
        print(f"\n💾 AI RESULTS EXPORTED:")
        print(f"   📊 Full results: {filename}")
        
        # Show some sample results
        print(f"\n🔍 SAMPLE AI RESULTS:")
        successful_results = [r for r in results if 'error' not in r and r.get('email')]
        
        for i, result in enumerate(successful_results[:5], 1):
            domain = result['url'].split('/')[2].replace('www.', '')
            email = result.get('email', 'No email')
            confidence = result.get('email_confidence', 0)
            social = result.get('social_platform', 'No social')
            print(f"   {i}. {domain}: {email} (conf: {confidence:.2f}) | {social}")
        
        if len(successful_results) > 5:
            print(f"   ... and {len(successful_results) - 5} more with emails found")
        
        # Comparison note
        print(f"\n📈 COMPARISON READY:")
        print(f"   • This AI extraction can be compared with the previous CSS/regex approach")
        print(f"   • AI approach: More semantic understanding, potentially higher accuracy")
        print(f"   • Traditional approach: Faster, more predictable, lower cost")
        
    except Exception as e:
        print(f"❌ AI extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🤖 Pittsburgh Coffee Shops AI Email Extractor")
    print("📁 Processing: pghshopsfromapi.csv")
    print("🧠 Using: Groq Llama-3.1-8B-Instant via crawl4ai")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⭐ AI extraction interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Script failed: {str(e)}")
