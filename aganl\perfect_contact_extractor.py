"""
Perfect Contact Extractor - The Ultimate Solution
Combines guaranteed page checking with intelligent early stopping.
"""

import asyncio
import json
import re
import csv
from datetime import datetime
from typing import Dict, List, Optional, Union, Tuple
from urllib.parse import urljoin

from crawl4ai import As<PERSON><PERSON>eb<PERSON><PERSON><PERSON>, CrawlerRunConfig
from crawl4ai import JsonCssExtractionStrategy


class PerfectContactExtractor:
    """
    The perfect contact extractor that combines:
    - Guaranteed page checking (no missed contact pages)
    - Intelligent early stopping (maximum efficiency)
    - Robust extraction (CSS + regex fallbacks)
    """
    
    def __init__(self, batch_size: int = 50, max_concurrent: int = 6):
        """Initialize the perfect extractor."""
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        
        # Guaranteed pages to check in priority order
        self.priority_pages = [
            ('', 10),              # Main page - highest priority
            ('/contact-us', 9),    # Primary contact page
            ('/contact', 9),       # Alternative contact page
            ('/about', 7),         # About page often has contact info
        ]
    
    async def extract_perfect(self, urls: Union[str, List[str]]) -> List[Dict]:
        """
        Perfect extraction with guaranteed coverage and smart stopping.
        
        Args:
            urls: URLs to process
            
        Returns:
            List of results with emails and social media
        """
        if isinstance(urls, str):
            urls = [urls]
        
        print(f"⭐ PERFECT CONTACT EXTRACTION")
        print(f"📊 Processing {len(urls)} URLs with guaranteed coverage + smart stopping")
        print(f"⚡ Max concurrent per batch: {self.max_concurrent}")
        
        all_results = []
        total_start = datetime.now()
        
        # Process URLs in batches
        for i in range(0, len(urls), self.batch_size):
            batch = urls[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (len(urls) + self.batch_size - 1) // self.batch_size
            
            print(f"\n📦 Batch {batch_num}/{total_batches}: {len(batch)} URLs")
            
            batch_start = datetime.now()
            batch_results = await self._process_batch_perfect(batch)
            batch_duration = (datetime.now() - batch_start).total_seconds()
            
            all_results.extend(batch_results)
            
            print(f"   ✅ Completed in {batch_duration:.2f}s ({len(batch)/batch_duration:.2f} URLs/sec)")
            
            # Small delay between batches
            if i + self.batch_size < len(urls):
                await asyncio.sleep(1)
        
        total_duration = (datetime.now() - total_start).total_seconds()
        
        print(f"\n🎉 PERFECT EXTRACTION COMPLETED!")
        print(f"   • Total URLs: {len(urls)}")
        print(f"   • Total time: {total_duration:.2f}s ({total_duration/60:.1f} min)")
        print(f"   • Overall rate: {len(urls)/total_duration:.2f} URLs/second")
        
        return all_results
    
    async def _process_batch_perfect(self, urls: List[str]) -> List[Dict]:
        """Process a batch with perfect intelligence."""
        
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_single(url: str) -> Dict:
            async with semaphore:
                return await self._extract_single_perfect(url)
        
        tasks = [process_single(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append({
                    "url": urls[i],
                    "error": str(result),
                    "timestamp": datetime.now().isoformat()
                })
            else:
                final_results.append(result)
        
        return final_results
    
    async def _extract_single_perfect(self, url: str) -> Dict:
        """Extract from single URL with perfect intelligence."""
        
        try:
            # Build guaranteed pages to check in priority order
            pages_to_check = []
            for page_path, priority in self.priority_pages:
                if page_path == '':
                    page_url = url
                else:
                    page_url = urljoin(url, page_path)
                pages_to_check.append((page_url, priority))
            
            domain = url.split('/')[2].replace('www.', '')
            print(f"   ⭐ {domain}: Checking {len(pages_to_check)} pages with smart stopping")
            
            best_email = None
            best_social = None
            pages_checked = 0
            pages_with_content = 0
            
            async with AsyncWebCrawler(verbose=False) as crawler:
                for page_url, priority in pages_to_check:
                    try:
                        print(f"     🔍 Checking: {page_url} (priority: {priority})")
                        
                        # Extract from this page
                        result = await asyncio.wait_for(
                            self._extract_from_page_perfect(crawler, page_url),
                            timeout=15.0
                        )
                        
                        pages_checked += 1
                        
                        if result.get('has_content'):
                            pages_with_content += 1
                        
                        # Update best contacts
                        if result.get('email') and not best_email:
                            best_email = result['email']
                            print(f"     📧 Found email: {best_email['email']}")
                        
                        if result.get('social') and not best_social:
                            best_social = result['social']
                            print(f"     🌐 Found social: {best_social['platform']}")
                        
                        # Smart early stopping logic
                        if best_email and best_social:
                            print(f"     ⚡ Perfect stop: Found both contacts after {pages_checked} pages")
                            break
                        elif pages_checked >= 2 and best_email:
                            # If we have email after checking main + contact page, that's often enough
                            print(f"     ⚡ Smart stop: Have email after checking key pages")
                            # But continue if we haven't checked contact-us page yet
                            remaining_contact_pages = [p for p in pages_to_check[pages_checked:] if 'contact' in p[0]]
                            if not remaining_contact_pages:
                                break
                        
                    except asyncio.TimeoutError:
                        print(f"     ⏰ Timeout: {page_url}")
                        continue
                    except Exception as e:
                        error_msg = str(e)
                        # Handle specific network connectivity issues
                        if "ERR_CONNECTION_CLOSED" in error_msg or "ERR_CONNECTION_REFUSED" in error_msg:
                            print(f"     🌐 Connection failed: {page_url} - website may be down")
                        elif "net::" in error_msg:
                            print(f"     🌐 Network error: {page_url} - {error_msg.split('net::')[1].split(' ')[0]}")
                        else:
                            print(f"     ❌ Error: {page_url} - {str(e)}")
                        continue
            
            print(f"     ✅ {domain}: Checked {pages_checked}/{len(pages_to_check)} pages, {pages_with_content} had content")
            
            return {
                "url": url,
                "timestamp": datetime.now().isoformat(),
                "email": best_email,
                "social_media": best_social,
                "pages_available": len(pages_to_check),
                "pages_checked": pages_checked,
                "pages_with_content": pages_with_content,
                "efficiency": f"{pages_checked}/{len(pages_to_check)}",
                "success": True
            }
        
        except Exception as e:
            error_msg = str(e)
            # Handle specific network connectivity issues with better messaging
            if "ERR_CONNECTION_CLOSED" in error_msg or "ERR_CONNECTION_REFUSED" in error_msg:
                error_msg = "Connection failed - website unreachable"
            elif "net::" in error_msg:
                error_msg = f"Network error - {error_msg.split('net::')[1].split(' ')[0]}"

            return {
                "url": url,
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            }
    
    async def _extract_from_page_perfect(self, crawler: AsyncWebCrawler, url: str) -> Dict:
        """Perfect extraction with comprehensive strategies."""
        
        # Enhanced CSS extraction strategy
        css_schema = {
            "name": "Perfect Contact",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "emails",
                    "selector": "a[href^='mailto:'], [data-email], [href*='@']",
                    "type": "list",
                    "fields": [{"name": "email", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "social",
                    "selector": "a[href*='instagram.com'], a[href*='facebook.com'], a[href*='twitter.com'], a[href*='x.com']",
                    "type": "list",
                    "fields": [{"name": "url", "type": "attribute", "attribute": "href"}]
                }
            ]
        }
        
        strategy = JsonCssExtractionStrategy(css_schema, verbose=False)
        config = CrawlerRunConfig(extraction_strategy=strategy)
        
        result = await crawler.arun(url=url, config=config)
        
        best_email = None
        best_social = None
        has_content = False
        
        if result.success:
            has_content = True
            
            # Process CSS results
            if result.extracted_content:
                try:
                    data = json.loads(result.extracted_content)
                    if data and len(data) > 0:
                        css_data = data[0]
                        
                        # Extract emails
                        emails = set()
                        for email_item in css_data.get('emails', []):
                            if isinstance(email_item, dict):
                                email = email_item.get('email', '').replace('mailto:', '').strip().lower()
                                if self._is_valid_email(email):
                                    emails.add(email)
                        
                        if emails:
                            best_email = self._select_best_email(list(emails))
                        
                        # Extract social links
                        social_links = set()
                        for social_item in css_data.get('social', []):
                            if isinstance(social_item, dict):
                                social_url = social_item.get('url', '').strip()
                                if social_url and self._is_valid_social_url(social_url):
                                    social_links.add(social_url)
                        
                        if social_links:
                            best_social = self._select_best_social(list(social_links))
                
                except json.JSONDecodeError:
                    pass
            
            # Perfect fallback: Comprehensive regex extraction
            if (not best_email or not best_social) and hasattr(result, 'cleaned_html'):
                content = result.cleaned_html
                
                # Perfect email regex patterns
                if not best_email:
                    email_patterns = [
                        r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b',  # Standard email
                        r'(?:email|contact|reach|write|talk)[\s:]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',  # After keywords
                        r'mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',  # Mailto links
                    ]
                    
                    all_emails = set()
                    for pattern in email_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        for match in matches:
                            email = match if isinstance(match, str) else match[0] if isinstance(match, tuple) else str(match)
                            if self._is_valid_email(email.lower()):
                                all_emails.add(email.lower())
                    
                    if all_emails:
                        best_email = self._select_best_email(list(all_emails))
                
                # Perfect social regex
                if not best_social:
                    social_pattern = r'https?://(?:www\.)?(?:instagram|facebook|twitter|x)\.com/[^\s<>"\']+' 
                    social_matches = re.findall(social_pattern, content, re.IGNORECASE)
                    valid_socials = [s for s in social_matches[:5] if self._is_valid_social_url(s)]
                    if valid_socials:
                        best_social = self._select_best_social(valid_socials)
        
        return {
            'email': best_email,
            'social': best_social,
            'has_content': has_content
        }
    
    def _is_valid_email(self, email: str) -> bool:
        """Perfect email validation."""
        if not email or len(email) < 5 or len(email) > 50:
            return False
        
        # Enhanced blacklist
        blacklist = ['.svg', '.png', '.jpg', '.jpeg', '.gif', '.css', '.js', '.pdf', '.doc', '.zip']
        if any(ext in email for ext in blacklist):
            return False
        
        # Must have exactly one @
        if '@' not in email or email.count('@') != 1:
            return False
        
        # Perfect format validation
        return bool(re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email))
    
    def _is_valid_social_url(self, url: str) -> bool:
        """Perfect social media URL validation."""
        if not url or len(url) < 10:
            return False
        
        platforms = ['instagram.com', 'facebook.com', 'twitter.com', 'x.com']
        if not any(platform in url.lower() for platform in platforms):
            return False
        
        # Avoid post/photo/status URLs
        avoid_patterns = ['/p/', '/posts/', '/photo/', '/status/', '/tweet/', '/reel/']
        if any(pattern in url.lower() for pattern in avoid_patterns):
            return False
        
        return True
    
    def _select_best_email(self, emails: List[str]) -> Optional[Dict]:
        """Perfect email selection with enhanced priority."""
        if not emails:
            return None
        
        # Perfect priority prefixes
        priority_prefixes = ['contact', 'info', 'hello', 'admin', 'support', 'talk', 'team']
        
        for prefix in priority_prefixes:
            for email in emails:
                if email.startswith(f"{prefix}@"):
                    return {"email": email, "confidence": 0.95}
        
        # Return shortest, most professional email
        best = min(emails, key=lambda x: (len(x), x.count('.'), 'noreply' in x))
        confidence = 0.85 if 'noreply' not in best else 0.70
        return {"email": best, "confidence": confidence}
    
    def _select_best_social(self, social_links: List[str]) -> Optional[Dict]:
        """Perfect social media selection."""
        if not social_links:
            return None
        
        # Platform priority
        priority = ['instagram.com', 'facebook.com', 'twitter.com', 'x.com']
        
        for platform in priority:
            for link in social_links:
                if platform in link.lower():
                    platform_name = platform.split('.')[0]
                    if platform_name == 'x':
                        platform_name = 'twitter'  # Normalize X to Twitter
                    handle = self._extract_handle(link)
                    return {
                        "platform": platform_name,
                        "url": link,
                        "handle": handle,
                        "confidence": 0.90
                    }
        
        return None
    
    def _extract_handle(self, url: str) -> Optional[str]:
        """Extract social media handle."""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            path_parts = [p for p in parsed.path.strip('/').split('/') if p]
            if path_parts:
                return f"@{path_parts[0]}"
        except:
            pass
        return None
    
    def export_to_csv(self, results: List[Dict], filename: str):
        """Export results to CSV with perfect details."""
        if not results:
            return
        
        rows = []
        for result in results:
            if 'error' in result:
                continue
            
            email = result.get('email', {})
            social = result.get('social_media', {})
            
            row = {
                'url': result.get('url', ''),
                'email': email.get('email', '') if email else '',
                'email_confidence': email.get('confidence', '') if email else '',
                'social_platform': social.get('platform', '') if social else '',
                'social_handle': social.get('handle', '') if social else '',
                'social_url': social.get('url', '') if social else '',
                'social_confidence': social.get('confidence', '') if social else '',
                'pages_available': result.get('pages_available', 0),
                'pages_checked': result.get('pages_checked', 0),
                'pages_with_content': result.get('pages_with_content', 0),
                'efficiency': result.get('efficiency', ''),
                'timestamp': result.get('timestamp', '')
            }
            rows.append(row)
        
        if rows:
            fieldnames = list(rows[0].keys())
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(rows)
            print(f"📊 Results exported to {filename}")
    
    def print_summary(self, results: List[Dict]):
        """Print perfect extraction summary."""
        total = len(results)
        successful = len([r for r in results if 'error' not in r])
        errors = total - successful
        
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        
        total_available = sum(r.get('pages_available', 0) for r in results if 'error' not in r)
        total_checked = sum(r.get('pages_checked', 0) for r in results if 'error' not in r)
        
        avg_available = total_available / successful if successful > 0 else 0
        avg_checked = total_checked / successful if successful > 0 else 0
        efficiency = (1 - (total_checked / total_available)) * 100 if total_available > 0 else 0
        
        early_stops = len([r for r in results if 'error' not in r and r.get('pages_checked', 0) < r.get('pages_available', 0)])
        
        print(f"\n📊 PERFECT EXTRACTION SUMMARY:")
        print(f"   • Total URLs: {total}")
        print(f"   • Successful: {successful}")
        print(f"   • Errors: {errors}")
        
        if successful > 0:
            print(f"   • Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
            print(f"   • Social media found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
            print(f"   • Both found: {len([r for r in results if 'error' not in r and r.get('email') and r.get('social_media')])}/{successful}")
            print(f"   • Average pages available: {avg_available:.1f}")
            print(f"   • Average pages checked: {avg_checked:.1f}")
            print(f"   • Early stops: {early_stops}/{successful} ({early_stops/successful*100:.1f}%)")
            print(f"   • Efficiency gain: {efficiency:.1f}% fewer pages checked")


# Perfect function for production use
async def extract_contacts_perfect(urls: Union[str, List[str]], 
                                 batch_size: int = 50,
                                 max_concurrent: int = 6) -> List[Dict]:
    """
    Perfect contact extraction with guaranteed coverage and smart stopping.
    
    Args:
        urls: URLs to process
        batch_size: URLs per batch
        max_concurrent: Concurrent requests per batch
        
    Returns:
        List of extraction results
    """
    extractor = PerfectContactExtractor(batch_size=batch_size, max_concurrent=max_concurrent)
    return await extractor.extract_perfect(urls)
