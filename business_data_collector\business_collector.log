2025-07-31 23:36:07,437 - __main__ - INFO - ============================================================
2025-07-31 23:36:07,447 - __main__ - INFO - BUSINESS DATA COLLECTION STARTED
2025-07-31 23:36:07,447 - __main__ - INFO - ============================================================
2025-07-31 23:36:07,447 - __main__ - INFO - Starting Google Places API collection...
2025-07-31 23:36:07,448 - collectors.google_places - INFO - Searching for restaurant businesses...
2025-07-31 23:36:07,650 - collectors.google_places - ERROR - Request error for restaurant: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:08,653 - collectors.google_places - INFO - Searching for store businesses...
2025-07-31 23:36:08,720 - collectors.google_places - ERROR - Request error for store: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:09,720 - collectors.google_places - INFO - Searching for establishment businesses...
2025-07-31 23:36:09,775 - collectors.google_places - ERROR - Request error for establishment: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:10,776 - collectors.google_places - INFO - Searching for food businesses...
2025-07-31 23:36:10,833 - collectors.google_places - ERROR - Request error for food: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:11,834 - collectors.google_places - INFO - Searching for lodging businesses...
2025-07-31 23:36:11,889 - collectors.google_places - ERROR - Request error for lodging: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:12,891 - collectors.google_places - INFO - Searching for health businesses...
2025-07-31 23:36:12,950 - collectors.google_places - ERROR - Request error for health: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:13,951 - collectors.google_places - INFO - Searching for finance businesses...
2025-07-31 23:36:14,004 - collectors.google_places - ERROR - Request error for finance: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:15,005 - collectors.google_places - INFO - Searching for beauty_salon businesses...
2025-07-31 23:36:15,049 - collectors.google_places - ERROR - Request error for beauty_salon: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:16,050 - collectors.google_places - INFO - Searching for car_repair businesses...
2025-07-31 23:36:16,259 - collectors.google_places - ERROR - Request error for car_repair: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:17,260 - collectors.google_places - INFO - Searching for gym businesses...
2025-07-31 23:36:17,440 - collectors.google_places - ERROR - Request error for gym: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:18,441 - collectors.google_places - INFO - Searching for laundry businesses...
2025-07-31 23:36:18,670 - collectors.google_places - ERROR - Request error for laundry: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:19,671 - collectors.google_places - INFO - Searching for lawyer businesses...
2025-07-31 23:36:19,720 - collectors.google_places - ERROR - Request error for lawyer: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:20,721 - collectors.google_places - INFO - Searching for real_estate_agency businesses...
2025-07-31 23:36:20,766 - collectors.google_places - ERROR - Request error for real_estate_agency: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:21,766 - collectors.google_places - INFO - Searching for veterinary_care businesses...
2025-07-31 23:36:21,809 - collectors.google_places - ERROR - Request error for veterinary_care: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:22,810 - collectors.google_places - INFO - Collected 0 businesses from Google Places API
2025-07-31 23:36:22,810 - __main__ - INFO - Google Places API: Collected 0 businesses
2025-07-31 23:36:22,811 - __main__ - INFO - Starting Overpass API collection...
2025-07-31 23:36:22,815 - collectors.overpass_api - INFO - Collecting amenities from Overpass API...
2025-07-31 23:36:27,400 - collectors.overpass_api - INFO - Collecting shops from Overpass API...
2025-07-31 23:36:33,739 - collectors.overpass_api - INFO - Collected 5450 businesses from Overpass API
2025-07-31 23:36:33,739 - __main__ - INFO - Overpass API: Collected 5450 businesses
2025-07-31 23:36:33,740 - __main__ - INFO - Starting Yellow Pages collection...
2025-07-31 23:36:33,740 - collectors.yellow_pages - INFO - Searching Yellow Pages for restaurants in Pittsburgh, PA...
2025-07-31 23:36:34,125 - collectors.yellow_pages - ERROR - Request error for restaurants page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=restaurants&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:36,127 - collectors.yellow_pages - INFO - Searching Yellow Pages for retail stores in Pittsburgh, PA...
2025-07-31 23:36:36,160 - collectors.yellow_pages - ERROR - Request error for retail stores page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=retail+stores&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:38,161 - collectors.yellow_pages - INFO - Searching Yellow Pages for auto repair in Pittsburgh, PA...
2025-07-31 23:36:38,197 - collectors.yellow_pages - ERROR - Request error for auto repair page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=auto+repair&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:40,198 - collectors.yellow_pages - INFO - Searching Yellow Pages for beauty salons in Pittsburgh, PA...
2025-07-31 23:36:40,234 - collectors.yellow_pages - ERROR - Request error for beauty salons page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=beauty+salons&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:42,236 - collectors.yellow_pages - INFO - Searching Yellow Pages for medical services in Pittsburgh, PA...
2025-07-31 23:36:42,279 - collectors.yellow_pages - ERROR - Request error for medical services page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=medical+services&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:44,281 - collectors.yellow_pages - INFO - Searching Yellow Pages for professional services in Pittsburgh, PA...
2025-07-31 23:36:44,314 - collectors.yellow_pages - ERROR - Request error for professional services page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=professional+services&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:46,315 - collectors.yellow_pages - INFO - Searching Yellow Pages for home services in Pittsburgh, PA...
2025-07-31 23:36:46,355 - collectors.yellow_pages - ERROR - Request error for home services page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=home+services&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:48,356 - collectors.yellow_pages - INFO - Searching Yellow Pages for financial services in Pittsburgh, PA...
2025-07-31 23:36:48,391 - collectors.yellow_pages - ERROR - Request error for financial services page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=financial+services&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:50,392 - collectors.yellow_pages - INFO - Collected 0 businesses from Yellow Pages
2025-07-31 23:36:50,392 - __main__ - INFO - Yellow Pages: Collected 0 businesses
2025-07-31 23:36:50,394 - __main__ - INFO - Total businesses collected from all sources: 5450
2025-07-31 23:36:50,395 - __main__ - INFO - Starting processing of 5450 businesses...
2025-07-31 23:36:50,396 - __main__ - INFO - Filtering businesses...
2025-07-31 23:36:50,399 - utils.filtering - INFO - Starting filtering of 5450 businesses...
2025-07-31 23:36:50,576 - utils.filtering - INFO - After filtering: 2590 businesses remain
2025-07-31 23:36:50,577 - __main__ - INFO - Deduplicating businesses...
2025-07-31 23:36:50,577 - utils.deduplication - INFO - Starting deduplication of 2590 businesses...
2025-07-31 23:36:50,940 - utils.deduplication - INFO - After exact duplicate removal: 2563 businesses
2025-07-31 23:38:53,752 - utils.deduplication - INFO - After fuzzy duplicate removal: 2349 businesses
2025-07-31 23:38:53,753 - __main__ - INFO - Processing complete: 2349 unique businesses
2025-07-31 23:38:53,753 - __main__ - INFO - Exporting results...
2025-07-31 23:38:53,811 - utils.data_export - INFO - Exported 2349 businesses to output\pittsburgh_businesses.csv
2025-07-31 23:38:53,812 - __main__ - INFO - CSV exported to: output\pittsburgh_businesses.csv
2025-07-31 23:38:53,857 - utils.data_export - INFO - Exported 2349 businesses to output\businesses_20250731_233853.json
2025-07-31 23:38:53,857 - __main__ - INFO - JSON exported to: output\businesses_20250731_233853.json
2025-07-31 23:38:53,862 - utils.data_export - INFO - Exported summary report to output\business_summary_20250731_233853.txt
2025-07-31 23:38:53,862 - __main__ - INFO - Summary report exported to: output\business_summary_20250731_233853.txt
2025-07-31 23:38:53,862 - __main__ - INFO - ============================================================
2025-07-31 23:38:53,863 - __main__ - INFO - BUSINESS DATA COLLECTION COMPLETED
2025-07-31 23:38:53,863 - __main__ - INFO - ============================================================
2025-07-31 23:38:53,863 - __main__ - INFO - Total runtime: 0:02:46.415274
2025-07-31 23:38:53,864 - __main__ - INFO - Raw businesses collected: 5450
2025-07-31 23:38:53,864 - __main__ - INFO - Final processed businesses: 2349
2025-07-31 23:38:53,864 - __main__ - INFO - Final breakdown by source:
2025-07-31 23:38:53,865 - __main__ - INFO -   overpass_api: 2349 businesses
2025-07-31 23:45:39,742 - __main__ - INFO - ============================================================
2025-07-31 23:45:39,743 - __main__ - INFO - BUSINESS DATA COLLECTION STARTED
2025-07-31 23:45:39,743 - __main__ - INFO - ============================================================
2025-07-31 23:45:39,743 - __main__ - INFO - Starting Google Places API collection...
2025-07-31 23:45:39,743 - collectors.google_places - INFO - Searching for restaurant businesses...
2025-07-31 23:45:40,003 - collectors.google_places - ERROR - API key forbidden (403) for restaurant. Check API key and billing.
2025-07-31 23:45:40,003 - collectors.google_places - INFO - Switching to legacy API for restaurant
2025-07-31 23:45:40,434 - collectors.google_places - INFO - Legacy API collected 20 businesses for restaurant
2025-07-31 23:45:41,435 - collectors.google_places - INFO - Searching for store businesses...
2025-07-31 23:45:41,736 - collectors.google_places - INFO - Legacy API collected 20 businesses for store
2025-07-31 23:45:42,737 - collectors.google_places - INFO - Searching for establishment businesses...
2025-07-31 23:45:43,051 - collectors.google_places - INFO - Legacy API collected 20 businesses for establishment
2025-07-31 23:45:44,052 - collectors.google_places - INFO - Searching for food businesses...
2025-07-31 23:45:44,199 - collectors.google_places - INFO - Legacy API collected 20 businesses for food
2025-07-31 23:45:45,200 - collectors.google_places - INFO - Searching for lodging businesses...
2025-07-31 23:45:45,495 - collectors.google_places - INFO - Legacy API collected 20 businesses for lodging
2025-07-31 23:45:46,496 - collectors.google_places - INFO - Searching for health businesses...
2025-07-31 23:45:46,617 - collectors.google_places - INFO - Legacy API collected 20 businesses for health
2025-07-31 23:45:47,618 - collectors.google_places - INFO - Searching for finance businesses...
2025-07-31 23:45:47,846 - collectors.google_places - INFO - Legacy API collected 20 businesses for finance
2025-07-31 23:45:48,847 - collectors.google_places - INFO - Searching for beauty_salon businesses...
2025-07-31 23:45:49,280 - collectors.google_places - INFO - Legacy API collected 20 businesses for beauty_salon
2025-07-31 23:45:50,281 - collectors.google_places - INFO - Searching for car_repair businesses...
2025-07-31 23:45:50,553 - collectors.google_places - INFO - Legacy API collected 20 businesses for car_repair
2025-07-31 23:45:51,554 - collectors.google_places - INFO - Searching for gym businesses...
2025-07-31 23:45:51,754 - collectors.google_places - INFO - Legacy API collected 20 businesses for gym
2025-07-31 23:45:52,755 - collectors.google_places - INFO - Searching for laundry businesses...
2025-07-31 23:45:53,070 - collectors.google_places - INFO - Legacy API collected 20 businesses for laundry
2025-07-31 23:45:54,071 - collectors.google_places - INFO - Searching for lawyer businesses...
2025-07-31 23:45:54,335 - collectors.google_places - INFO - Legacy API collected 20 businesses for lawyer
2025-07-31 23:45:55,336 - collectors.google_places - INFO - Searching for real_estate_agency businesses...
2025-07-31 23:45:55,628 - collectors.google_places - INFO - Legacy API collected 20 businesses for real_estate_agency
2025-07-31 23:45:56,628 - collectors.google_places - INFO - Searching for veterinary_care businesses...
2025-07-31 23:45:56,976 - collectors.google_places - INFO - Legacy API collected 20 businesses for veterinary_care
2025-07-31 23:45:57,977 - collectors.google_places - INFO - Collected 280 businesses from Google Places API
2025-07-31 23:45:57,978 - __main__ - INFO - Google Places API: Collected 280 businesses
2025-07-31 23:45:57,980 - __main__ - INFO - Starting Overpass API collection...
2025-07-31 23:45:57,980 - collectors.overpass_api - INFO - Collecting amenities from Overpass API...
2025-07-31 23:46:01,999 - collectors.overpass_api - INFO - Collecting shops from Overpass API...
2025-07-31 23:46:04,208 - collectors.overpass_api - INFO - Collected 5450 businesses from Overpass API
2025-07-31 23:46:04,209 - __main__ - INFO - Overpass API: Collected 5450 businesses
2025-07-31 23:46:04,209 - __main__ - INFO - Starting Yellow Pages collection...
2025-07-31 23:46:18,687 - collectors.yellow_pages - INFO - Searching Yellow Pages for restaurants in Pittsburgh, PA...
2025-07-31 23:46:23,740 - collectors.yellow_pages - INFO - Searching Yellow Pages for retail stores in Pittsburgh, PA...
2025-07-31 23:46:27,710 - collectors.yellow_pages - INFO - Searching Yellow Pages for auto repair in Pittsburgh, PA...
2025-07-31 23:46:31,281 - collectors.yellow_pages - INFO - Searching Yellow Pages for beauty salons in Pittsburgh, PA...
2025-07-31 23:46:34,969 - collectors.yellow_pages - INFO - Searching Yellow Pages for medical services in Pittsburgh, PA...
2025-07-31 23:46:38,436 - collectors.yellow_pages - INFO - Searching Yellow Pages for professional services in Pittsburgh, PA...
2025-07-31 23:46:41,987 - collectors.yellow_pages - INFO - Searching Yellow Pages for home services in Pittsburgh, PA...
2025-07-31 23:46:45,500 - collectors.yellow_pages - INFO - Searching Yellow Pages for financial services in Pittsburgh, PA...
2025-07-31 23:46:49,451 - collectors.yellow_pages - INFO - Collected 0 businesses from Yellow Pages
2025-07-31 23:46:49,452 - __main__ - INFO - Yellow Pages: Collected 0 businesses
2025-07-31 23:46:49,452 - __main__ - INFO - Total businesses collected from all sources: 5730
2025-07-31 23:46:49,452 - __main__ - INFO - Starting processing of 5730 businesses...
2025-07-31 23:46:49,453 - __main__ - INFO - Filtering businesses...
2025-07-31 23:46:49,454 - utils.filtering - INFO - Starting filtering of 5730 businesses...
2025-07-31 23:46:49,616 - utils.filtering - INFO - After filtering: 2786 businesses remain
2025-07-31 23:46:49,617 - __main__ - INFO - Deduplicating businesses...
2025-07-31 23:46:49,617 - utils.deduplication - INFO - Starting deduplication of 2786 businesses...
2025-07-31 23:46:50,043 - utils.deduplication - INFO - After exact duplicate removal: 2733 businesses
2025-07-31 23:50:22,963 - utils.deduplication - INFO - After fuzzy duplicate removal: 2504 businesses
2025-07-31 23:50:22,963 - __main__ - INFO - Processing complete: 2504 unique businesses
2025-07-31 23:50:22,964 - __main__ - INFO - Exporting results...
2025-07-31 23:50:23,019 - utils.data_export - INFO - Exported 2504 businesses to output\pittsburgh_businesses.csv
2025-07-31 23:50:23,020 - __main__ - INFO - CSV exported to: output\pittsburgh_businesses.csv
2025-07-31 23:50:23,085 - utils.data_export - INFO - Exported 2504 businesses to output\businesses_20250731_235023.json
2025-07-31 23:50:23,086 - __main__ - INFO - JSON exported to: output\businesses_20250731_235023.json
2025-07-31 23:50:23,090 - utils.data_export - INFO - Exported summary report to output\business_summary_20250731_235023.txt
2025-07-31 23:50:23,091 - __main__ - INFO - Summary report exported to: output\business_summary_20250731_235023.txt
2025-07-31 23:50:23,091 - __main__ - INFO - ============================================================
2025-07-31 23:50:23,091 - __main__ - INFO - BUSINESS DATA COLLECTION COMPLETED
2025-07-31 23:50:23,091 - __main__ - INFO - ============================================================
2025-07-31 23:50:23,092 - __main__ - INFO - Total runtime: 0:04:43.347659
2025-07-31 23:50:23,092 - __main__ - INFO - Raw businesses collected: 5730
2025-07-31 23:50:23,093 - __main__ - INFO - Final processed businesses: 2504
2025-07-31 23:50:23,094 - __main__ - INFO - Final breakdown by source:
2025-07-31 23:50:23,094 - __main__ - INFO -   google_places_legacy: 153 businesses
2025-07-31 23:50:23,095 - __main__ - INFO -   overpass_api: 2337 businesses
2025-07-31 23:50:23,095 - __main__ - INFO -   overpass_api,google_places_legacy: 14 businesses
2025-08-01 00:04:24,014 - __main__ - INFO - ============================================================
2025-08-01 00:04:24,015 - __main__ - INFO - BUSINESS DATA COLLECTION STARTED
2025-08-01 00:04:24,015 - __main__ - INFO - ============================================================
2025-08-01 00:04:24,015 - __main__ - INFO - Starting Google Places API collection...
2025-08-01 00:04:24,015 - collectors.google_places - INFO - Searching for restaurant businesses...
2025-08-01 00:04:24,318 - collectors.google_places - ERROR - API key forbidden (403) for restaurant. Check API key and billing.
2025-08-01 00:04:24,319 - collectors.google_places - INFO - Switching to legacy API for restaurant
2025-08-01 00:04:24,785 - collectors.google_places - INFO - Legacy API collected 20 businesses for restaurant
2025-08-01 00:04:25,786 - collectors.google_places - INFO - Searching for store businesses...
2025-08-01 00:04:26,267 - collectors.google_places - INFO - Legacy API collected 20 businesses for store
2025-08-01 00:04:27,268 - collectors.google_places - INFO - Searching for establishment businesses...
2025-08-01 00:04:27,599 - collectors.google_places - INFO - Legacy API collected 20 businesses for establishment
2025-08-01 00:04:28,600 - collectors.google_places - INFO - Searching for food businesses...
2025-08-01 00:04:28,775 - collectors.google_places - INFO - Legacy API collected 20 businesses for food
2025-08-01 00:04:29,776 - collectors.google_places - INFO - Searching for lodging businesses...
2025-08-01 00:04:30,010 - collectors.google_places - INFO - Legacy API collected 20 businesses for lodging
2025-08-01 00:04:31,012 - collectors.google_places - INFO - Searching for health businesses...
2025-08-01 00:04:31,148 - collectors.google_places - INFO - Legacy API collected 20 businesses for health
2025-08-01 00:04:32,150 - collectors.google_places - INFO - Searching for finance businesses...
2025-08-01 00:04:32,298 - collectors.google_places - INFO - Legacy API collected 20 businesses for finance
2025-08-01 00:04:33,300 - collectors.google_places - INFO - Searching for beauty_salon businesses...
2025-08-01 00:04:33,640 - collectors.google_places - INFO - Legacy API collected 20 businesses for beauty_salon
2025-08-01 00:04:34,641 - collectors.google_places - INFO - Searching for car_repair businesses...
2025-08-01 00:04:34,873 - collectors.google_places - INFO - Legacy API collected 20 businesses for car_repair
2025-08-01 00:04:35,874 - collectors.google_places - INFO - Searching for gym businesses...
2025-08-01 00:04:36,100 - collectors.google_places - INFO - Legacy API collected 20 businesses for gym
2025-08-01 00:04:37,101 - collectors.google_places - INFO - Searching for laundry businesses...
2025-08-01 00:04:37,329 - collectors.google_places - INFO - Legacy API collected 20 businesses for laundry
2025-08-01 00:04:38,330 - collectors.google_places - INFO - Searching for lawyer businesses...
2025-08-01 00:04:38,531 - collectors.google_places - INFO - Legacy API collected 20 businesses for lawyer
2025-08-01 00:04:39,532 - collectors.google_places - INFO - Searching for real_estate_agency businesses...
2025-08-01 00:04:40,058 - collectors.google_places - INFO - Legacy API collected 20 businesses for real_estate_agency
2025-08-01 00:04:41,059 - collectors.google_places - INFO - Searching for veterinary_care businesses...
2025-08-01 00:04:41,646 - collectors.google_places - INFO - Legacy API collected 20 businesses for veterinary_care
2025-08-01 00:04:42,647 - collectors.google_places - INFO - Collected 280 businesses from Google Places API
2025-08-01 00:04:42,648 - __main__ - INFO - Google Places API: Collected 280 businesses
2025-08-01 00:04:42,660 - __main__ - INFO - Starting Overpass API collection...
2025-08-01 00:04:42,661 - collectors.overpass_api - INFO - Collecting amenities from Overpass API...
2025-08-01 00:04:47,194 - collectors.overpass_api - INFO - Collecting shops from Overpass API...
2025-08-01 00:04:50,067 - collectors.overpass_api - INFO - Collected 5450 businesses from Overpass API
2025-08-01 00:04:50,068 - __main__ - INFO - Overpass API: Collected 5450 businesses
2025-08-01 00:04:50,069 - __main__ - INFO - Starting Yellow Pages collection...
2025-08-01 00:04:51,246 - collectors.yellow_pages - INFO - Searching Yellow Pages for restaurants in Pittsburgh, PA...
2025-08-01 00:05:04,381 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-01 00:05:07,163 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:05:07,164 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:05:07,165 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:05:28,227 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:05:28,250 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:05:45,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:05:45,437 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:05:54,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:05:54,754 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:05:54,765 - collectors.yellow_pages - INFO - Extracted 29 businesses from restaurants page 1
2025-08-01 00:06:00,053 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:06:00,060 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:06:23,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:06:23,330 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:06:58,449 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:06:58,489 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:06:58,505 - collectors.yellow_pages - INFO - Extracted 29 businesses from restaurants page 2
2025-08-01 00:07:02,513 - collectors.yellow_pages - INFO - Searching Yellow Pages for retail stores in Pittsburgh, PA...
2025-08-01 00:07:06,119 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:07:06,121 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:07:06,125 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:07:34,399 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:07:34,406 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:08:03,075 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:08:03,081 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:08:10,382 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:08:10,388 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:08:10,416 - collectors.yellow_pages - INFO - Extracted 30 businesses from retail stores page 1
2025-08-01 00:08:15,135 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:08:15,137 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:08:15,142 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:08:58,673 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:08:58,687 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:08:59,035 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:08:59,040 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:09:29,324 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:09:29,330 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:09:29,357 - collectors.yellow_pages - INFO - Extracted 28 businesses from retail stores page 2
2025-08-01 00:09:33,362 - collectors.yellow_pages - INFO - Searching Yellow Pages for auto repair in Pittsburgh, PA...
2025-08-01 00:09:37,077 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:09:37,089 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:09:37,098 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:09:37,110 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:09:48,498 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:09:48,500 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:10:26,133 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:10:26,141 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:10:27,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:10:27,338 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:10:39,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:10:39,872 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:10:39,897 - collectors.yellow_pages - INFO - Extracted 27 businesses from auto repair page 1
2025-08-01 00:10:46,615 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:10:46,633 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:11:08,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:11:08,255 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:11:33,028 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:11:33,030 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:11:33,046 - collectors.yellow_pages - INFO - Extracted 30 businesses from auto repair page 2
2025-08-01 00:11:37,064 - collectors.yellow_pages - INFO - Searching Yellow Pages for beauty salons in Pittsburgh, PA...
2025-08-01 00:11:41,018 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:11:41,026 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:12:18,142 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:12:18,146 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:12:31,079 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:12:31,082 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:12:31,094 - collectors.yellow_pages - INFO - Extracted 29 businesses from beauty salons page 1
2025-08-01 00:12:36,051 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:12:36,053 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:12:36,056 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:12:49,104 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:12:49,120 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:13:01,458 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:13:01,460 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:13:23,330 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:13:23,340 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:13:23,365 - collectors.yellow_pages - INFO - Extracted 30 businesses from beauty salons page 2
2025-08-01 00:13:27,393 - collectors.yellow_pages - INFO - Searching Yellow Pages for medical services in Pittsburgh, PA...
2025-08-01 00:13:30,662 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:13:30,664 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:13:57,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:13:57,391 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:14:25,371 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:14:25,376 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:14:25,392 - collectors.yellow_pages - INFO - Extracted 30 businesses from medical services page 1
2025-08-01 00:14:30,422 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:14:30,425 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:14:30,430 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:15:00,495 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:15:00,499 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:15:04,081 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:15:04,086 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:16:44,032 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:16:44,035 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:16:44,045 - collectors.yellow_pages - INFO - Extracted 28 businesses from medical services page 2
2025-08-01 00:16:48,074 - collectors.yellow_pages - INFO - Searching Yellow Pages for professional services in Pittsburgh, PA...
2025-08-01 00:16:50,847 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:16:50,848 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:17:29,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:17:29,999 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:17:47,473 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:17:47,476 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:17:47,490 - collectors.yellow_pages - INFO - Extracted 29 businesses from professional services page 1
2025-08-01 00:17:52,264 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:17:52,267 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:18:23,972 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:18:23,996 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:18:40,581 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:18:40,583 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:18:40,594 - collectors.yellow_pages - INFO - Extracted 25 businesses from professional services page 2
2025-08-01 00:18:44,613 - collectors.yellow_pages - INFO - Searching Yellow Pages for home services in Pittsburgh, PA...
2025-08-01 00:18:47,522 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:18:47,524 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:18:47,532 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:18:59,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:18:59,725 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:19:22,971 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:19:22,973 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:19:40,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:19:40,802 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:19:40,816 - collectors.yellow_pages - INFO - Extracted 28 businesses from home services page 1
2025-08-01 00:19:45,282 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:19:45,283 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:20:17,341 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:20:17,345 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:21:24,224 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:21:24,228 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:21:24,243 - collectors.yellow_pages - INFO - Extracted 28 businesses from home services page 2
2025-08-01 00:21:28,262 - collectors.yellow_pages - INFO - Searching Yellow Pages for financial services in Pittsburgh, PA...
2025-08-01 00:21:31,586 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:21:31,588 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:21:31,591 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:22:04,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:22:04,369 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:22:05,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:22:05,393 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:22:15,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:22:15,534 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:22:15,550 - collectors.yellow_pages - INFO - Extracted 24 businesses from financial services page 1
2025-08-01 00:22:20,314 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:22:20,319 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:22:20,322 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o-mini; provider = openai
2025-08-01 00:23:14,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:23:14,727 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:23:17,266 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:23:17,287 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:23:29,771 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 00:23:29,809 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-01 00:23:29,832 - collectors.yellow_pages - INFO - Extracted 30 businesses from financial services page 2
2025-08-01 00:23:34,916 - collectors.yellow_pages - INFO - Collected 454 businesses from Yellow Pages
2025-08-01 00:23:34,918 - __main__ - INFO - Yellow Pages: Collected 454 businesses
2025-08-01 00:23:34,918 - __main__ - INFO - Total businesses collected from all sources: 6184
2025-08-01 00:23:34,918 - __main__ - INFO - Starting processing of 6184 businesses...
2025-08-01 00:23:34,920 - __main__ - INFO - Filtering businesses...
2025-08-01 00:23:34,923 - utils.filtering - INFO - Starting filtering of 6184 businesses...
2025-08-01 00:23:35,160 - utils.filtering - INFO - After filtering: 3232 businesses remain
2025-08-01 00:23:35,161 - __main__ - INFO - Deduplicating businesses...
2025-08-01 00:23:35,161 - utils.deduplication - INFO - Starting deduplication of 3232 businesses...
2025-08-01 00:23:35,757 - utils.deduplication - INFO - After exact duplicate removal: 3179 businesses
2025-08-01 00:28:05,461 - utils.deduplication - INFO - After fuzzy duplicate removal: 2890 businesses
2025-08-01 00:28:05,461 - __main__ - INFO - Processing complete: 2890 unique businesses
2025-08-01 00:28:05,461 - __main__ - INFO - Exporting results...
2025-08-01 00:28:05,585 - utils.data_export - INFO - Exported 2890 businesses to output\pittsburgh_businesses.csv
2025-08-01 00:28:05,588 - __main__ - INFO - CSV exported to: output\pittsburgh_businesses.csv
2025-08-01 00:28:05,711 - utils.data_export - INFO - Exported 2890 businesses to output\businesses_20250801_002805.json
2025-08-01 00:28:05,711 - __main__ - INFO - JSON exported to: output\businesses_20250801_002805.json
2025-08-01 00:28:05,720 - utils.data_export - INFO - Exported summary report to output\business_summary_20250801_002805.txt
2025-08-01 00:28:05,721 - __main__ - INFO - Summary report exported to: output\business_summary_20250801_002805.txt
2025-08-01 00:28:05,723 - __main__ - INFO - ============================================================
2025-08-01 00:28:05,723 - __main__ - INFO - BUSINESS DATA COLLECTION COMPLETED
2025-08-01 00:28:05,723 - __main__ - INFO - ============================================================
2025-08-01 00:28:05,724 - __main__ - INFO - Total runtime: 0:23:41.707826
2025-08-01 00:28:05,725 - __main__ - INFO - Raw businesses collected: 6184
2025-08-01 00:28:05,725 - __main__ - INFO - Final processed businesses: 2890
2025-08-01 00:28:05,727 - __main__ - INFO - Final breakdown by source:
2025-08-01 00:28:05,728 - __main__ - INFO -   google_places_legacy: 150 businesses
2025-08-01 00:28:05,728 - __main__ - INFO -   google_places_legacy,overpass_api: 11 businesses
2025-08-01 00:28:05,729 - __main__ - INFO -   google_places_legacy,yellow_pages_llm: 3 businesses
2025-08-01 00:28:05,729 - __main__ - INFO -   overpass_api: 2306 businesses
2025-08-01 00:28:05,729 - __main__ - INFO -   overpass_api,yellow_pages_llm: 31 businesses
2025-08-01 00:28:05,730 - __main__ - INFO -   yellow_pages_llm: 386 businesses
2025-08-01 00:28:05,730 - __main__ - INFO -   yellow_pages_llm,google_places_legacy,overpass_api: 3 businesses
