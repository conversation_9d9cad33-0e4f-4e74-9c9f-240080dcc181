"""
Unified business category system for mapping user-friendly categories 
to API-specific formats for Google Places, Overpass API, and Yellow Pages.
"""
from typing import Dict, List, Set, Optional
from dataclasses import dataclass
from enum import Enum


@dataclass
class CategoryMapping:
    """Mapping for a business category across different APIs"""
    name: str
    description: str
    google_places_types: List[str]
    overpass_amenities: List[str]
    overpass_shops: List[str]
    overpass_offices: List[str]  # New: office tags for professional services
    yellow_pages_terms: List[str]
    keywords: List[str]  # For search functionality


class CategoryGroup(Enum):
    """High-level category groups for organization"""
    FOOD_DINING = "Food & Dining"
    RETAIL_SHOPPING = "Retail & Shopping"
    HEALTH_MEDICAL = "Health & Medical"
    AUTOMOTIVE = "Automotive"
    PROFESSIONAL_SERVICES = "Professional Services"
    HOME_SERVICES = "Home & Garden Services"
    BEAUTY_WELLNESS = "Beauty & Wellness"
    ENTERTAINMENT = "Entertainment & Recreation"
    FINANCIAL = "Financial Services"
    EDUCATION = "Education & Training"
    ACCOMMODATION = "Accommodation & Travel"
    TECHNOLOGY = "Technology & Electronics"
    GOVERNMENT = "Government & Public Services"


# Unified category definitions
UNIFIED_CATEGORIES = {
    # Food & Dining
    "restaurants": CategoryMapping(
        name="Restaurants",
        description="Full-service restaurants and dining establishments",
        google_places_types=["restaurant", "meal_takeaway", "meal_delivery"],
        overpass_amenities=["restaurant"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["restaurants"],
        keywords=["restaurant", "dining", "food", "eat", "cuisine"]
    ),

    "cafes_coffee": CategoryMapping(
        name="Cafes & Coffee Shops",
        description="Coffee shops, cafes, and casual dining spots",
        google_places_types=["cafe"],
        overpass_amenities=["cafe"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["coffee shops"],
        keywords=["cafe", "coffee", "espresso", "latte", "tea"]
    ),

    "bars_pubs": CategoryMapping(
        name="Bars & Pubs",
        description="Bars, pubs, and drinking establishments",
        google_places_types=["bar", "night_club"],
        overpass_amenities=["bar", "pub", "nightclub"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["bars"],
        keywords=["bar", "pub", "drinks", "alcohol", "nightlife"]
    ),

    "fast_food": CategoryMapping(
        name="Fast Food",
        description="Quick service restaurants and fast food chains",
        google_places_types=["meal_takeaway"],
        overpass_amenities=["fast_food"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["fast food"],
        keywords=["fast food", "quick", "takeaway", "drive through"]
    ),
    
    # Retail & Shopping
    "retail_stores": CategoryMapping(
        name="Retail Stores",
        description="General retail and merchandise stores",
        google_places_types=["store", "shopping_mall", "department_store"],
        overpass_amenities=["marketplace"],
        overpass_shops=["convenience", "general", "department_store"],
        overpass_offices=[],
        yellow_pages_terms=["retail stores", "shopping", "merchandise"],
        keywords=["retail", "store", "shop", "shopping", "merchandise"]
    ),

    "clothing_fashion": CategoryMapping(
        name="Clothing & Fashion",
        description="Clothing stores, fashion boutiques, and apparel shops",
        google_places_types=["clothing_store"],
        overpass_amenities=[],
        overpass_shops=["clothes", "shoes", "jewelry"],
        overpass_offices=[],
        yellow_pages_terms=["clothing stores", "fashion", "apparel"],
        keywords=["clothing", "fashion", "apparel", "shoes", "jewelry"]
    ),

    "electronics": CategoryMapping(
        name="Electronics & Technology",
        description="Electronics stores, computer shops, and tech retailers",
        google_places_types=["electronics_store"],
        overpass_amenities=[],
        overpass_shops=["electronics", "computer", "mobile_phone"],
        overpass_offices=[],
        yellow_pages_terms=["electronics", "computers", "technology"],
        keywords=["electronics", "computer", "tech", "mobile", "gadgets"]
    ),

    "grocery_supermarket": CategoryMapping(
        name="Grocery & Supermarkets",
        description="Grocery stores, supermarkets, and food retailers",
        google_places_types=["supermarket"],
        overpass_amenities=[],
        overpass_shops=["supermarket", "convenience", "greengrocer"],
        overpass_offices=[],
        yellow_pages_terms=["grocery stores", "supermarkets", "food stores"],
        keywords=["grocery", "supermarket", "food store", "market"]
    ),
    
    # Health & Medical
    "medical_services": CategoryMapping(
        name="Medical Services",
        description="Doctors, clinics, and medical practices",
        google_places_types=["doctor", "hospital"],
        overpass_amenities=["hospital", "clinic", "doctors"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["medical services", "doctors", "healthcare"],
        keywords=["medical", "doctor", "clinic", "healthcare", "physician"]
    ),

    "dentists": CategoryMapping(
        name="Dentists",
        description="Dental offices and oral health services",
        google_places_types=["dentist"],
        overpass_amenities=["dentist"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["dentists", "dental services"],
        keywords=["dentist", "dental", "teeth", "oral health"]
    ),

    "pharmacies": CategoryMapping(
        name="Pharmacies",
        description="Pharmacies and drug stores",
        google_places_types=["pharmacy"],
        overpass_amenities=["pharmacy"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["drug stores"],
        keywords=["pharmacy", "drugs", "medication", "prescriptions"]
    ),

    "veterinary": CategoryMapping(
        name="Veterinary Services",
        description="Veterinarians and animal hospitals",
        google_places_types=["veterinary_care"],
        overpass_amenities=["veterinary"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["veterinary", "animal hospitals"],
        keywords=["veterinary", "vet", "animal", "pet care"]
    ),
    
    # Automotive
    "auto_repair": CategoryMapping(
        name="Auto Repair",
        description="Auto repair shops and automotive services",
        google_places_types=["car_repair"],
        overpass_amenities=[],
        overpass_shops=["car_repair"],
        overpass_offices=[],
        yellow_pages_terms=["auto repair", "car service", "automotive"],
        keywords=["auto repair", "car repair", "automotive", "mechanic"]
    ),

    "gas_stations": CategoryMapping(
        name="Gas Stations",
        description="Fuel stations and gas stations",
        google_places_types=["gas_station"],
        overpass_amenities=["fuel"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["gas stations", "fuel"],
        keywords=["gas station", "fuel", "gasoline", "petrol"]
    ),

    "car_wash": CategoryMapping(
        name="Car Wash",
        description="Car wash and auto detailing services",
        google_places_types=["car_wash"],
        overpass_amenities=["car_wash"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["car wash", "auto detailing"],
        keywords=["car wash", "auto detailing", "car cleaning"]
    ),
    
    # Financial Services (Specific Categories)
    "banks": CategoryMapping(
        name="Banks",
        description="Banks, credit unions, and banking services",
        google_places_types=["bank"],
        overpass_amenities=["bank"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["banks", "credit unions", "banking services"],
        keywords=["bank", "banking", "credit union", "financial institution"]
    ),

    "insurance_agencies": CategoryMapping(
        name="Insurance Agencies",
        description="Insurance companies, agencies, and brokers",
        google_places_types=["insurance_agency"],
        overpass_amenities=[],
        overpass_shops=[],
        overpass_offices=["insurance"],
        yellow_pages_terms=["insurance agencies", "insurance companies", "insurance brokers"],
        keywords=["insurance", "insurance agency", "insurance broker", "coverage"]
    ),

    "accounting_firms": CategoryMapping(
        name="Accounting Firms",
        description="Accounting firms, tax services, and bookkeeping",
        google_places_types=["accounting"],
        overpass_amenities=[],
        overpass_shops=[],
        overpass_offices=["accountant", "tax", "tax_advisor"],
        yellow_pages_terms=["accounting firms", "accountants", "tax services", "bookkeeping"],
        keywords=["accounting", "accountant", "tax", "bookkeeping", "CPA"]
    ),

    "financial_advisors": CategoryMapping(
        name="Financial Advisors",
        description="Investment firms, financial planning, and wealth management",
        google_places_types=[],  # 'finance' is Table 2 only, can't be used in searches
        overpass_amenities=[],
        overpass_shops=[],
        overpass_offices=["financial", "financial_advisor"],
        yellow_pages_terms=["financial advisors", "investment firms", "wealth management", "financial planning"],
        keywords=["financial advisor", "investment", "wealth management", "financial planning"]
    ),

    "law_firms": CategoryMapping(
        name="Law Firms",
        description="Law firms, attorneys, and legal services",
        google_places_types=["lawyer"],
        overpass_amenities=[],
        overpass_shops=[],
        overpass_offices=["lawyer"],
        yellow_pages_terms=["law firms", "attorneys", "lawyers", "legal services"],
        keywords=["lawyer", "attorney", "legal", "law firm", "legal services"]
    ),

    "real_estate_agencies": CategoryMapping(
        name="Real Estate Agencies",
        description="Real estate agencies, realtors, and property services",
        google_places_types=["real_estate_agency"],
        overpass_amenities=[],
        overpass_shops=[],
        overpass_offices=["real_estate_agent", "estate_agent"],
        yellow_pages_terms=["real estate agencies", "realtors", "property management"],
        keywords=["real estate", "property", "realtor", "homes", "real estate agent"]
    ),
    
    # Beauty & Wellness
    "beauty_salons": CategoryMapping(
        name="Beauty Salons",
        description="Hair salons, beauty parlors, and cosmetic services",
        google_places_types=["beauty_salon", "hair_care"],
        overpass_amenities=[],
        overpass_shops=["hairdresser", "beauty"],
        overpass_offices=[],
        yellow_pages_terms=["beauty salons", "hair salons"],
        keywords=["beauty salon", "hair salon", "hairdresser", "cosmetics"]
    ),

    "spas_wellness": CategoryMapping(
        name="Spas & Wellness",
        description="Spas, massage therapy, and wellness centers",
        google_places_types=["spa"],
        overpass_amenities=[],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["spas", "massage therapy", "wellness"],
        keywords=["spa", "massage", "wellness", "relaxation"]
    ),

    "gyms_fitness": CategoryMapping(
        name="Gyms & Fitness",
        description="Gyms, fitness centers, and exercise facilities",
        google_places_types=["gym"],
        overpass_amenities=[],
        overpass_shops=["sports"],
        overpass_offices=[],
        yellow_pages_terms=["gyms", "fitness centers", "exercise"],
        keywords=["gym", "fitness", "exercise", "workout", "training"]
    ),

    # Accommodation & Travel
    "hotels": CategoryMapping(
        name="Hotels & Lodging",
        description="Hotels, motels, bed & breakfasts, and lodging facilities",
        google_places_types=["lodging", "hotel", "bed_and_breakfast", "motel"],
        overpass_amenities=[],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["hotel", "motel"],
        keywords=["hotel", "motel", "lodging", "accommodation", "stay"]
    ),

    "travel_agencies": CategoryMapping(
        name="Travel Agencies",
        description="Travel agencies and tour operators",
        google_places_types=["travel_agency", "tour_agency"],
        overpass_amenities=[],
        overpass_shops=["travel_agency"],
        overpass_offices=["travel_agent"],
        yellow_pages_terms=["travel agencies", "tour operators", "travel services"],
        keywords=["travel agency", "tour", "vacation", "travel planning"]
    ),

    # Government & Public Services
    "government_offices": CategoryMapping(
        name="Government Offices",
        description="Government offices, city halls, and public administration",
        google_places_types=["government_office", "city_hall", "local_government_office"],
        overpass_amenities=["townhall"],
        overpass_shops=[],
        overpass_offices=["government"],
        yellow_pages_terms=["government offices", "city hall", "public services"],
        keywords=["government", "city hall", "public office", "administration"]
    ),

    "post_offices": CategoryMapping(
        name="Post Offices",
        description="Post offices and postal services",
        google_places_types=["post_office"],
        overpass_amenities=["post_office"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["post offices", "postal services", "mail"],
        keywords=["post office", "mail", "postal", "shipping"]
    ),

    "police_stations": CategoryMapping(
        name="Police Stations",
        description="Police stations and law enforcement facilities",
        google_places_types=["police"],
        overpass_amenities=["police"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["police stations", "law enforcement"],
        keywords=["police", "law enforcement", "police station"]
    ),

    "fire_stations": CategoryMapping(
        name="Fire Stations",
        description="Fire stations and emergency services",
        google_places_types=["fire_station"],
        overpass_amenities=["fire_station"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["fire stations", "fire department", "emergency services"],
        keywords=["fire station", "fire department", "emergency", "firefighters"]
    ),

    # Education
    "schools": CategoryMapping(
        name="Schools",
        description="Primary and secondary schools",
        google_places_types=["school", "primary_school", "secondary_school"],
        overpass_amenities=["school"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["schools", "elementary schools", "high schools"],
        keywords=["school", "elementary", "high school", "education"]
    ),

    "universities": CategoryMapping(
        name="Universities & Colleges",
        description="Universities, colleges, and higher education institutions",
        google_places_types=["university"],
        overpass_amenities=["university", "college"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["universities", "colleges", "higher education"],
        keywords=["university", "college", "higher education", "campus"]
    ),

    "libraries": CategoryMapping(
        name="Libraries",
        description="Public and private libraries",
        google_places_types=["library"],
        overpass_amenities=["library"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["libraries", "public libraries"],
        keywords=["library", "books", "reading", "public library"]
    ),

    # Entertainment & Recreation
    "movie_theaters": CategoryMapping(
        name="Movie Theaters",
        description="Movie theaters and cinemas",
        google_places_types=["movie_theater"],
        overpass_amenities=["cinema"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["movie theaters", "cinemas", "movies"],
        keywords=["movie theater", "cinema", "movies", "films"]
    ),

    "amusement_parks": CategoryMapping(
        name="Amusement Parks",
        description="Amusement parks, theme parks, and entertainment venues",
        google_places_types=["amusement_park", "amusement_center"],
        overpass_amenities=[],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["amusement parks", "theme parks", "entertainment"],
        keywords=["amusement park", "theme park", "rides", "entertainment"]
    ),

    "casinos": CategoryMapping(
        name="Casinos",
        description="Casinos and gambling establishments",
        google_places_types=["casino"],
        overpass_amenities=["casino"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["casinos", "gambling", "gaming"],
        keywords=["casino", "gambling", "gaming", "slots"]
    ),

    "bowling_alleys": CategoryMapping(
        name="Bowling Alleys",
        description="Bowling alleys and bowling centers",
        google_places_types=["bowling_alley"],
        overpass_amenities=[],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["bowling alleys", "bowling centers"],
        keywords=["bowling", "bowling alley", "lanes", "strikes"]
    ),

    # Professional Services
    "electricians": CategoryMapping(
        name="Electricians",
        description="Electrical contractors and electricians",
        google_places_types=["electrician"],
        overpass_amenities=[],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["electricians", "electrical contractors"],
        keywords=["electrician", "electrical", "wiring", "electrical contractor"]
    ),

    "plumbers": CategoryMapping(
        name="Plumbers",
        description="Plumbing contractors and plumbers",
        google_places_types=["plumber"],
        overpass_amenities=[],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["plumbers", "plumbing contractors"],
        keywords=["plumber", "plumbing", "pipes", "plumbing contractor"]
    ),

    "locksmiths": CategoryMapping(
        name="Locksmiths",
        description="Locksmith services and security specialists",
        google_places_types=["locksmith"],
        overpass_amenities=[],
        overpass_shops=["locksmith"],
        overpass_offices=[],
        yellow_pages_terms=["locksmiths", "lock services", "security"],
        keywords=["locksmith", "locks", "keys", "security"]
    ),

    "florists": CategoryMapping(
        name="Florists",
        description="Flower shops and floral services",
        google_places_types=["florist"],
        overpass_amenities=[],
        overpass_shops=["florist"],
        overpass_offices=[],
        yellow_pages_terms=["florists", "flower shops", "floral services"],
        keywords=["florist", "flowers", "floral", "bouquet"]
    ),

    # Additional Services
    "storage_facilities": CategoryMapping(
        name="Storage Facilities",
        description="Self-storage and storage rental facilities",
        google_places_types=["storage"],
        overpass_amenities=[],
        overpass_shops=["storage_rental"],
        overpass_offices=[],
        yellow_pages_terms=["storage facilities", "self storage", "storage rental"],
        keywords=["storage", "self storage", "storage unit", "warehouse"]
    ),

    "laundromats": CategoryMapping(
        name="Laundromats",
        description="Laundromats and laundry services",
        google_places_types=["laundry"],
        overpass_amenities=[],
        overpass_shops=["laundry", "dry_cleaning"],
        overpass_offices=[],
        yellow_pages_terms=["laundromats", "laundry services", "dry cleaning"],
        keywords=["laundromat", "laundry", "dry cleaning", "wash"]
    ),

    "atms": CategoryMapping(
        name="ATMs",
        description="Automated teller machines and cash points",
        google_places_types=["atm"],
        overpass_amenities=["atm"],
        overpass_shops=[],
        overpass_offices=[],
        yellow_pages_terms=["ATMs", "cash machines"],
        keywords=["ATM", "cash machine", "bank machine", "cash point"]
    ),
}


# Group categories by their primary group
CATEGORY_GROUPS = {
    CategoryGroup.FOOD_DINING: [
        "restaurants", "cafes_coffee", "bars_pubs", "fast_food"
    ],
    CategoryGroup.RETAIL_SHOPPING: [
        "retail_stores", "clothing_fashion", "electronics", "grocery_supermarket"
    ],
    CategoryGroup.HEALTH_MEDICAL: [
        "medical_services", "dentists", "pharmacies", "veterinary"
    ],
    CategoryGroup.AUTOMOTIVE: [
        "auto_repair", "gas_stations", "car_wash"
    ],
    CategoryGroup.FINANCIAL: [
        "banks", "insurance_agencies", "accounting_firms", "financial_advisors", "atms"
    ],
    CategoryGroup.PROFESSIONAL_SERVICES: [
        "law_firms", "real_estate_agencies", "electricians", "plumbers", "locksmiths"
    ],
    CategoryGroup.BEAUTY_WELLNESS: [
        "beauty_salons", "spas_wellness", "gyms_fitness"
    ],
    CategoryGroup.ACCOMMODATION: [
        "hotels", "travel_agencies"
    ],
    CategoryGroup.GOVERNMENT: [
        "government_offices", "post_offices", "police_stations", "fire_stations"
    ],
    CategoryGroup.EDUCATION: [
        "schools", "universities", "libraries"
    ],
    CategoryGroup.ENTERTAINMENT: [
        "movie_theaters", "amusement_parks", "casinos", "bowling_alleys"
    ],
    CategoryGroup.HOME_SERVICES: [
        "florists", "storage_facilities", "laundromats"
    ],
}


class CategoryManager:
    """Manager class for business category operations"""
    
    def __init__(self):
        self.categories = UNIFIED_CATEGORIES
        self.groups = CATEGORY_GROUPS
    
    def get_all_categories(self) -> Dict[str, CategoryMapping]:
        """Get all available categories"""
        return self.categories
    
    def get_category(self, category_id: str) -> Optional[CategoryMapping]:
        """Get a specific category by ID"""
        return self.categories.get(category_id)
    
    def get_categories_by_group(self, group: CategoryGroup) -> List[CategoryMapping]:
        """Get all categories in a specific group"""
        category_ids = self.groups.get(group, [])
        return [self.categories[cat_id] for cat_id in category_ids if cat_id in self.categories]
    
    def search_categories(self, query: str) -> List[CategoryMapping]:
        """Search categories by name, description, or keywords"""
        query = query.lower()
        results = []
        
        for category in self.categories.values():
            # Check name and description
            if (query in category.name.lower() or 
                query in category.description.lower()):
                results.append(category)
                continue
            
            # Check keywords
            if any(query in keyword.lower() for keyword in category.keywords):
                results.append(category)
                continue
        
        return results
    
    def get_google_places_types(self, category_ids: List[str]) -> List[str]:
        """Get Google Places types for given category IDs"""
        types = set()
        for cat_id in category_ids:
            if cat_id in self.categories:
                types.update(self.categories[cat_id].google_places_types)
        return list(types)
    
    def get_overpass_amenities(self, category_ids: List[str]) -> List[str]:
        """Get Overpass amenities for given category IDs"""
        amenities = set()
        for cat_id in category_ids:
            if cat_id in self.categories:
                amenities.update(self.categories[cat_id].overpass_amenities)
        return list(amenities)
    
    def get_overpass_shops(self, category_ids: List[str]) -> List[str]:
        """Get Overpass shops for given category IDs"""
        shops = set()
        for cat_id in category_ids:
            if cat_id in self.categories:
                shops.update(self.categories[cat_id].overpass_shops)
        return list(shops)

    def get_overpass_offices(self, category_ids: List[str]) -> List[str]:
        """Get Overpass offices for given category IDs"""
        offices = set()
        for cat_id in category_ids:
            if cat_id in self.categories:
                offices.update(self.categories[cat_id].overpass_offices)
        return list(offices)

    def get_yellow_pages_terms(self, category_ids: List[str]) -> List[str]:
        """Get Yellow Pages search terms for given category IDs"""
        terms = set()
        for cat_id in category_ids:
            if cat_id in self.categories:
                terms.update(self.categories[cat_id].yellow_pages_terms)
        return list(terms)
