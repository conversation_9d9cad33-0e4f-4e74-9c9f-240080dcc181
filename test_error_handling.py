"""
Test script to verify error handling fixes for both extraction methods.
"""

import asyncio
import sys
import os

# Add the aganl directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'aganl'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'business_data_collector'))

from perfect_contact_extractor import PerfectContactExtractor
from temp_ai_extract_emails_from_pgh_shops import AIContactExtractor


async def test_error_handling():
    """Test error handling with problematic URLs."""
    
    # Test URLs including the problematic one and some good ones
    test_urls = [
        "https://www.bigdogcoffeeshop.com/",  # This one fails
        "https://commonplacecoffee.com/",      # This one should work
        "https://nonexistent-website-12345.com/",  # This should fail
        "https://defer.coffee/"               # This one should work
    ]
    
    print("🧪 TESTING ERROR HANDLING FIXES")
    print("=" * 50)
    
    # Test traditional extractor
    print("\n📊 Testing Traditional Extractor:")
    traditional_extractor = PerfectContactExtractor(batch_size=2, max_concurrent=2)
    traditional_results = await traditional_extractor.extract_perfect(test_urls)
    
    print("\n📊 Traditional Results Summary:")
    for result in traditional_results:
        if result is None:
            print(f"   ❌ Unknown: Result was None")
            continue

        url = result.get('url', 'Unknown')
        domain = url.split('/')[2] if '://' in url else url

        if 'error' in result:
            print(f"   ❌ {domain}: {result['error']}")
        else:
            email_data = result.get('email')
            if email_data and isinstance(email_data, dict):
                email = email_data.get('email', 'No email')
            else:
                email = 'No email'
            print(f"   ✅ {domain}: {email}")
    
    # Test AI extractor
    print("\n🤖 Testing AI Extractor:")
    try:
        ai_extractor = AIContactExtractor(batch_size=2, max_concurrent=2)
        ai_results = await ai_extractor.extract_contacts_ai(test_urls)
        
        print("\n🤖 AI Results Summary:")
        for result in ai_results:
            url = result.get('url', 'Unknown')
            domain = url.split('/')[2] if '://' in url else url
            if 'error' in result:
                print(f"   ❌ {domain}: {result['error']}")
            else:
                email = result.get('email', 'No email')
                confidence = result.get('email_confidence')
                conf_str = f"{confidence:.2f}" if confidence is not None else "N/A"
                print(f"   ✅ {domain}: {email} (conf: {conf_str})")
    
    except Exception as e:
        print(f"   ❌ AI Extractor failed to initialize: {str(e)}")
        print("   (This might be due to missing Groq API key)")
    
    print("\n✅ Error handling test completed!")


if __name__ == "__main__":
    try:
        asyncio.run(test_error_handling())
    except KeyboardInterrupt:
        print("\n\n⭐ Test interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
