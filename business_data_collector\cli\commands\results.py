"""
Results management commands for the CLI
"""
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from typing import Optional
import os
import pandas as pd
from datetime import datetime
from pathlib import Path

console = Console()

# Create results app
results_app = typer.Typer(help="View and manage collection results")

# Results directory
RESULTS_DIR = Path("output")


@results_app.command("list")
def list_results():
    """
    📈 List all previous collection results
    """
    if not RESULTS_DIR.exists():
        console.print("[yellow]No results directory found[/yellow]")
        return

    # Find CSV files in results directory
    csv_files = list(RESULTS_DIR.glob("*.csv"))
    scraped_files = list(Path(".").glob("scraped_*.csv"))

    all_files = csv_files + scraped_files

    if not all_files:
        console.print("[yellow]No result files found[/yellow]")
        return

    table = Table(title="Collection Results", show_header=True, header_style="bold magenta")
    table.add_column("File", style="cyan", no_wrap=True)
    table.add_column("Type", style="yellow")
    table.add_column("Size", style="white")
    table.add_column("Modified", style="dim")
    table.add_column("Records", style="green")

    for file_path in sorted(all_files, key=lambda x: x.stat().st_mtime, reverse=True):
        try:
            stat = file_path.stat()
            size = f"{stat.st_size / 1024:.1f} KB"
            modified = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")

            # Determine file type
            if "scraped" in file_path.name:
                file_type = "Email Scraping"
            elif "businesses" in file_path.name:
                file_type = "Business Collection"
            else:
                file_type = "Unknown"

            # Count records
            try:
                df = pd.read_csv(file_path)
                record_count = str(len(df))
            except:
                record_count = "Error"

            table.add_row(
                file_path.name,
                file_type,
                size,
                modified,
                record_count
            )
        except Exception as e:
            console.print(f"[red]Error reading {file_path.name}: {e}[/red]")

    console.print(table)


@results_app.command("show")
def show_result(
    filename: str = typer.Argument(..., help="Result filename to display")
):
    """
    📊 Show details of a specific result file
    """
    # Look for file in results directory or current directory
    file_path = None
    if (RESULTS_DIR / filename).exists():
        file_path = RESULTS_DIR / filename
    elif Path(filename).exists():
        file_path = Path(filename)
    else:
        console.print(f"[red]File not found: {filename}[/red]")
        console.print("[yellow]Use 'results list' to see available files[/yellow]")
        raise typer.Exit(1)

    try:
        df = pd.read_csv(file_path)

        console.print(Panel(
            f"[bold]File:[/bold] {file_path.name}\n"
            f"[bold]Records:[/bold] {len(df)}\n"
            f"[bold]Columns:[/bold] {len(df.columns)}\n"
            f"[bold]Size:[/bold] {file_path.stat().st_size / 1024:.1f} KB",
            title="Result Details",
            border_style="blue"
        ))

        # Show column info
        console.print("\n[bold]Columns:[/bold]")
        for col in df.columns:
            non_null = df[col].notna().sum()
            console.print(f"  • {col}: {non_null}/{len(df)} non-null values")

        # Show sample data
        console.print("\n[bold]Sample Data (first 5 rows):[/bold]")
        console.print(df.head().to_string(index=False))

    except Exception as e:
        console.print(f"[red]Error reading file: {e}[/red]")


@results_app.command("clean")
def clean_results():
    """
    🧹 Clean up old result files
    """
    if not RESULTS_DIR.exists():
        console.print("[yellow]No results directory found[/yellow]")
        return

    # Find old files (older than 30 days)
    import time
    current_time = time.time()
    old_files = []

    for file_path in RESULTS_DIR.glob("*.csv"):
        if current_time - file_path.stat().st_mtime > 30 * 24 * 3600:  # 30 days
            old_files.append(file_path)

    if not old_files:
        console.print("[green]No old files to clean up[/green]")
        return

    console.print(f"[yellow]Found {len(old_files)} files older than 30 days[/yellow]")
    for file_path in old_files:
        console.print(f"  • {file_path.name}")

    if typer.confirm("Delete these files?"):
        for file_path in old_files:
            file_path.unlink()
        console.print(f"[green]✓ Deleted {len(old_files)} old files[/green]")
    else:
        console.print("[yellow]Cleanup cancelled[/yellow]")


@results_app.callback()
def results_callback():
    """Results management commands"""
    pass
