"""
Temporary script to extract emails from Pittsburgh coffee shops using the perfect contact extractor.
This script processes the pghshopsfromapi.csv file without modifying the main project.
"""

import asyncio
import csv
import sys
import os
from datetime import datetime

# Add the aganl directory to the path so we can import the extractor
sys.path.append(os.path.join(os.path.dirname(__file__), 'aganl'))

from perfect_contact_extractor import PerfectContactExtractor


def load_urls_from_pgh_csv(filename: str) -> list:
    """Load URLs from the Pittsburgh shops CSV file."""
    urls = []
    shop_data = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                website = row.get('website', '').strip()
                if website and website.startswith(('http://', 'https://')):
                    urls.append(website)
                    # Store additional shop info for later reference
                    shop_data.append({
                        'name': row.get('name', ''),
                        'neighborhood': row.get('neighborhood', ''),
                        'website': website,
                        'address': row.get('address', ''),
                        'uuid': row.get('uuid', '')
                    })
        
        print(f"✅ Loaded {len(urls)} valid website URLs from {filename}")
        print(f"📊 Total entries in CSV: {len(list(csv.DictReader(open(filename, 'r', encoding='utf-8'))))}")
        print(f"🌐 Entries with websites: {len(urls)}")
        
        return urls, shop_data
        
    except Exception as e:
        print(f"❌ Error loading URLs from {filename}: {e}")
        return [], []


async def extract_emails_from_pgh_shops():
    """Extract emails from Pittsburgh coffee shops."""
    print("🏢 PITTSBURGH COFFEE SHOPS EMAIL EXTRACTION")
    print("=" * 60)
    
    # Load URLs from the CSV file
    csv_filename = "pghshopsfromapi.csv"
    urls, shop_data = load_urls_from_pgh_csv(csv_filename)
    
    if not urls:
        print("❌ No valid URLs found in the CSV file.")
        return
    
    print(f"\n🎯 Processing {len(urls)} Pittsburgh coffee shop websites")
    print(f"⚙️  Using production settings for optimal performance")
    
    # Initialize the extractor with production settings
    extractor = PerfectContactExtractor(
        batch_size=25,      # Process 25 URLs per batch
        max_concurrent=5    # 5 concurrent requests per batch
    )
    
    # Start extraction
    start_time = datetime.now()
    print(f"\n🚀 Starting extraction at {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        results = await extractor.extract_perfect(urls)
        duration = (datetime.now() - start_time).total_seconds()
        
        print(f"\n✅ Extraction completed in {duration:.2f} seconds")
        print(f"⚡ Processing rate: {len(urls)/duration:.2f} URLs/second")
        
        # Print comprehensive summary
        extractor.print_summary(results)
        
        # Export results with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"pittsburgh_contacts_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        
        # Also create an emails-only file for easy review
        emails_only_filename = f"pittsburgh_emails_only_{timestamp}.csv"
        create_emails_only_file(results, shop_data, emails_only_filename)
        
        print(f"\n💾 RESULTS EXPORTED:")
        print(f"   📊 Full results: {filename}")
        print(f"   📧 Emails only: {emails_only_filename}")
        
        # Show some sample results
        print(f"\n🔍 SAMPLE RESULTS:")
        successful_results = [r for r in results if 'error' not in r and r.get('email')]
        
        for i, result in enumerate(successful_results[:5], 1):
            email = result.get('email', {})
            domain = result['url'].split('/')[2].replace('www.', '')
            print(f"   {i}. {domain}: {email.get('email', 'No email')}")
        
        if len(successful_results) > 5:
            print(f"   ... and {len(successful_results) - 5} more with emails found")
        
        # Scaling projections
        rate = len(urls) / duration
        print(f"\n📈 SCALING PROJECTIONS (at current rate):")
        print(f"   • 1,000 URLs: ~{1000/rate/60:.1f} minutes")
        print(f"   • 10,000 URLs: ~{10000/rate/60:.1f} minutes")
        
    except Exception as e:
        print(f"❌ Extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()


def create_emails_only_file(results, shop_data, filename):
    """Create a simplified CSV with just the emails found."""
    try:
        # Create a mapping of URLs to shop data
        url_to_shop = {shop['website']: shop for shop in shop_data}
        
        rows = []
        for result in results:
            if 'error' in result:
                continue
            
            email = result.get('email', {})
            if not email:
                continue
            
            url = result.get('url', '')
            shop_info = url_to_shop.get(url, {})
            
            row = {
                'name': shop_info.get('name', ''),
                'neighborhood': shop_info.get('neighborhood', ''),
                'website': url,
                'email': email.get('email', ''),
                'email_confidence': email.get('confidence', ''),
                'address': shop_info.get('address', ''),
                'pages_checked': result.get('pages_checked', 0),
                'timestamp': result.get('timestamp', '')
            }
            rows.append(row)
        
        if rows:
            fieldnames = list(rows[0].keys())
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(rows)
            print(f"📧 Emails-only file created: {filename} ({len(rows)} emails found)")
        else:
            print("📧 No emails found to export")
            
    except Exception as e:
        print(f"❌ Error creating emails-only file: {e}")


async def main():
    """Main function."""
    try:
        await extract_emails_from_pgh_shops()
        
        print(f"\n⭐ EXTRACTION COMPLETE!")
        print("✅ Check the generated CSV files for results")
        print("✅ The extractor used intelligent page discovery and early stopping")
        print("✅ Results include confidence scores and extraction metadata")
        
    except KeyboardInterrupt:
        print("\n\n⭐ Extraction interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 Pittsburgh Coffee Shops Email Extractor")
    print("📁 Processing: pghshopsfromapi.csv")
    print("🔧 Using: aganl/perfect_contact_extractor.py")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⭐ Script interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Script failed: {str(e)}")
