"""
Google Places API collector for business data.
"""
import requests
import time
import logging
from typing import List, Dict, Any, Optional
from config import (
    GOOGLE_PLACES_API_KEY,
    PITTSBURGH_LAT,
    PITTSBURGH_LNG,
    SEARCH_RADIUS_METERS,
    <PERSON>O<PERSON>LE_PLACES_DELAY,
    GOOGLE_PLACES_TYPES
)

logger = logging.getLogger(__name__)


class GooglePlacesCollector:
    """Collector for business data from Google Places API."""
    
    def __init__(self, api_key: str = GOOGLE_PLACES_API_KEY):
        """Initialize the Google Places collector.

        Args:
            api_key: Google Places API key
        """
        self.api_key = api_key
        self.base_url = "https://places.googleapis.com/v1/places:searchNearby"
        self.legacy_url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"
        self.session = requests.Session()
        self.use_legacy = False  # Flag to switch to legacy API if new API fails
        
    def collect_businesses(self, 
                          lat: float = PITTSBURGH_LAT,
                          lng: float = PITTSBURGH_LNG,
                          radius: int = SEARCH_RADIUS_METERS,
                          max_results: int = 20) -> List[Dict[str, Any]]:
        """Collect business data from Google Places API.
        
        Args:
            lat: Latitude of search center
            lng: Longitude of search center
            radius: Search radius in meters
            max_results: Maximum results per request
            
        Returns:
            List of business data dictionaries
        """
        businesses = []
        
        # Search for different business types
        for business_type in GOOGLE_PLACES_TYPES:
            logger.info(f"Searching for {business_type} businesses...")
            
            try:
                if self.use_legacy:
                    type_businesses = self._search_by_type_legacy(
                        business_type, lat, lng, radius, max_results
                    )
                else:
                    type_businesses = self._search_by_type(
                        business_type, lat, lng, radius, max_results
                    )

                    # If new API fails with 403, switch to legacy
                    if not type_businesses and not self.use_legacy:
                        logger.info(f"Switching to legacy API for {business_type}")
                        self.use_legacy = True
                        type_businesses = self._search_by_type_legacy(
                            business_type, lat, lng, radius, max_results
                        )

                businesses.extend(type_businesses)
                
                # Rate limiting
                time.sleep(GOOGLE_PLACES_DELAY)
                
            except Exception as e:
                logger.error(f"Error searching for {business_type}: {str(e)}")
                continue
                
        logger.info(f"Collected {len(businesses)} businesses from Google Places API")
        return businesses

    def _get_business_types(self) -> List[str]:
        """Get current business types being used"""
        return GOOGLE_PLACES_TYPES.copy()

    def _set_business_types(self, types: List[str]):
        """Temporarily set business types for collection"""
        # This is a bit of a hack, but we'll modify the global for this session
        global GOOGLE_PLACES_TYPES
        GOOGLE_PLACES_TYPES[:] = types
    
    def _search_by_type(self,
                       business_type: str,
                       lat: float,
                       lng: float,
                       radius: int,
                       max_results: int) -> List[Dict[str, Any]]:
        """Search for businesses of a specific type.
        
        Args:
            business_type: Type of business to search for
            lat: Latitude of search center
            lng: Longitude of search center
            radius: Search radius in meters
            max_results: Maximum results per request
            
        Returns:
            List of business data dictionaries
        """
        headers = {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': self.api_key,
            'X-Goog-FieldMask': (
                'places.displayName,'
                'places.formattedAddress,'
                'places.internationalPhoneNumber,'
                'places.nationalPhoneNumber,'
                'places.websiteUri,'
                'places.types,'
                'places.primaryType,'
                'places.businessStatus,'
                'places.location,'
                'places.rating,'
                'places.userRatingCount,'
                'places.priceLevel'
            )
        }
        
        payload = {
            "includedTypes": [business_type],
            "maxResultCount": max_results,
            "locationRestriction": {
                "circle": {
                    "center": {
                        "latitude": lat,
                        "longitude": lng
                    },
                    "radius": radius
                }
            },
            "rankPreference": "POPULARITY"
        }
        
        try:
            response = self.session.post(
                self.base_url,
                json=payload,
                headers=headers,
                timeout=30
            )

            # Log response details for debugging
            logger.debug(f"Response status: {response.status_code}")
            logger.debug(f"Response headers: {dict(response.headers)}")

            if response.status_code == 403:
                logger.error(f"API key forbidden (403) for {business_type}. Check API key and billing.")
                return []
            elif response.status_code == 400:
                logger.error(f"Bad request (400) for {business_type}. Response: {response.text}")
                return []

            response.raise_for_status()

            data = response.json()
            places = data.get('places', [])

            businesses = []
            for place in places:
                business = self._parse_place_data(place)
                if business:
                    businesses.append(business)

            logger.info(f"Successfully collected {len(businesses)} businesses for {business_type}")
            return businesses

        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for {business_type}: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response content: {e.response.text}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error for {business_type}: {str(e)}")
            return []
    
    def _parse_place_data(self, place: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse place data from Google Places API response.
        
        Args:
            place: Place data from API response
            
        Returns:
            Parsed business data dictionary or None if invalid
        """
        try:
            # Extract basic information
            display_name = place.get('displayName', {})
            name = display_name.get('text', '').strip()
            
            if not name:
                return None
                
            # Extract contact information
            address = place.get('formattedAddress', '').strip()
            phone = (place.get('internationalPhoneNumber') or 
                    place.get('nationalPhoneNumber', '')).strip()
            website = place.get('websiteUri', '').strip()
            
            # Extract business details
            types = place.get('types', [])
            primary_type = place.get('primaryType', '')
            business_status = place.get('businessStatus', '')
            
            # Extract location
            location = place.get('location', {})
            latitude = location.get('latitude')
            longitude = location.get('longitude')
            
            # Extract ratings
            rating = place.get('rating')
            user_rating_count = place.get('userRatingCount')
            price_level = place.get('priceLevel')
            
            business_data = {
                'name': name,
                'address': address,
                'phone': phone,
                'website': website,
                'types': types,
                'primary_type': primary_type,
                'business_status': business_status,
                'latitude': latitude,
                'longitude': longitude,
                'rating': rating,
                'user_rating_count': user_rating_count,
                'price_level': price_level,
                'source': 'google_places'
            }
            
            return business_data

        except Exception as e:
            logger.error(f"Error parsing place data: {str(e)}")
            return None

    def _search_by_type_legacy(self,
                              business_type: str,
                              lat: float,
                              lng: float,
                              radius: int,
                              max_results: int) -> List[Dict[str, Any]]:
        """Search for businesses using legacy Google Places API.

        Args:
            business_type: Type of business to search for
            lat: Latitude of search center
            lng: Longitude of search center
            radius: Search radius in meters
            max_results: Maximum results per request

        Returns:
            List of business data dictionaries
        """
        params = {
            'location': f"{lat},{lng}",
            'radius': radius,
            'type': business_type,
            'key': self.api_key
        }

        try:
            response = self.session.get(
                self.legacy_url,
                params=params,
                timeout=30
            )

            logger.debug(f"Legacy API response status: {response.status_code}")

            if response.status_code == 403:
                logger.error(f"Legacy API key forbidden (403) for {business_type}")
                return []

            response.raise_for_status()

            data = response.json()
            results = data.get('results', [])

            businesses = []
            for place in results[:max_results]:  # Limit results
                business = self._parse_legacy_place_data(place)
                if business:
                    businesses.append(business)

            logger.info(f"Legacy API collected {len(businesses)} businesses for {business_type}")
            return businesses

        except requests.exceptions.RequestException as e:
            logger.error(f"Legacy API request error for {business_type}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Legacy API unexpected error for {business_type}: {str(e)}")
            return []

    def _parse_legacy_place_data(self, place: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse place data from legacy Google Places API response.

        Args:
            place: Place data from legacy API response

        Returns:
            Parsed business data dictionary or None if invalid
        """
        try:
            name = place.get('name', '').strip()
            if not name:
                return None

            # Extract location
            geometry = place.get('geometry', {})
            location = geometry.get('location', {})
            latitude = location.get('lat')
            longitude = location.get('lng')

            # Extract other fields
            address = place.get('vicinity', '').strip()
            types = place.get('types', [])
            business_status = 'OPERATIONAL' if place.get('business_status') == 'OPERATIONAL' else 'UNKNOWN'
            rating = place.get('rating')
            user_rating_count = place.get('user_ratings_total')
            price_level = place.get('price_level')

            business_data = {
                'name': name,
                'address': address,
                'phone': '',  # Not available in legacy nearby search
                'website': '',  # Not available in legacy nearby search
                'types': types,
                'primary_type': types[0] if types else '',
                'business_status': business_status,
                'latitude': latitude,
                'longitude': longitude,
                'rating': rating,
                'user_rating_count': user_rating_count,
                'price_level': price_level,
                'source': 'google_places_legacy'
            }

            return business_data

        except Exception as e:
            logger.error(f"Error parsing legacy place data: {str(e)}")
            return None
