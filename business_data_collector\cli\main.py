"""
Main CLI application for Business Data Collector
"""
import typer
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from typing import Optional, List
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cli.commands.collect import collect_app
from cli.commands.categories import categories_app
from cli.commands.scrape import scrape_app
from cli.commands.config import config_app
from cli.commands.results import results_app

# Initialize console for rich output
console = Console()

# Create main app
app = typer.Typer(
    name="business-cli",
    help="🏢 Business Data Collector CLI - Collect business data and scrape contact information",
    rich_markup_mode="rich",
    no_args_is_help=True
)

# Add subcommands
app.add_typer(collect_app, name="collect", help="📊 Collect business data from various sources")
app.add_typer(categories_app, name="categories", help="📋 Manage and view business categories")
app.add_typer(scrape_app, name="scrape", help="📧 Scrape emails and contact info from businesses")
app.add_typer(config_app, name="config", help="⚙️ Manage CLI configuration")
app.add_typer(results_app, name="results", help="📈 View and manage collection results")


@app.callback()
def main(
    version: Optional[bool] = typer.Option(
        None, "--version", "-v", help="Show version information"
    )
):
    """
    🏢 Business Data Collector CLI
    
    A comprehensive tool for collecting business data and scraping contact information.
    """
    if version:
        console.print(Panel(
            Text("Business Data Collector CLI v1.0.0", style="bold blue"),
            title="Version Info",
            border_style="blue"
        ))
        raise typer.Exit()


def cli_main():
    """Entry point for the CLI application"""
    try:
        app()
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user[/yellow]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error: {str(e)}[/red]")
        raise typer.Exit(1)


if __name__ == "__main__":
    cli_main()
