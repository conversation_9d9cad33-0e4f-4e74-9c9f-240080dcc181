"""
Configuration management commands for the CLI
"""
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from typing import Optional, Dict, Any
import json
import os
from pathlib import Path

console = Console()

# Create config app
config_app = typer.Typer(help="Manage CLI configuration")

# Configuration file path
CONFIG_DIR = Path.home() / ".business-cli"
CONFIG_FILE = CONFIG_DIR / "config.json"

# Default configuration
DEFAULT_CONFIG = {
    "default_radius": 10.0,
    "default_sources": ["google", "overpass", "yellowpages"],
    "default_batch_size": 25,
    "default_concurrent": 5,
    "output_format": "CSV",
    "google_api_key": "",
    "openai_api_key": ""
}


def _load_config() -> Dict[str, Any]:
    """Load configuration from file"""
    if not CONFIG_FILE.exists():
        return DEFAULT_CONFIG.copy()

    try:
        with open(CONFIG_FILE, 'r') as f:
            config = json.load(f)
        # Merge with defaults to ensure all keys exist
        merged_config = DEFAULT_CONFIG.copy()
        merged_config.update(config)
        return merged_config
    except Exception as e:
        console.print(f"[red]Error loading config: {e}[/red]")
        return DEFAULT_CONFIG.copy()


def _save_config(config: Dict[str, Any]) -> bool:
    """Save configuration to file"""
    try:
        CONFIG_DIR.mkdir(exist_ok=True)
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
        return True
    except Exception as e:
        console.print(f"[red]Error saving config: {e}[/red]")
        return False


@config_app.command("show")
def show_config():
    """
    ⚙️ Show current configuration
    """
    config = _load_config()

    table = Table(title="Current CLI Configuration", show_header=True, header_style="bold yellow")
    table.add_column("Setting", style="cyan", no_wrap=True)
    table.add_column("Value", style="white")
    table.add_column("Description", style="dim")

    descriptions = {
        "default_radius": "Default search radius in miles",
        "default_sources": "Default data sources to use",
        "default_batch_size": "Default batch size for scraping",
        "default_concurrent": "Default concurrent requests",
        "output_format": "Default output format",
        "google_api_key": "Google Places API key",
        "openai_api_key": "OpenAI API key"
    }

    for key, value in config.items():
        if key.endswith('_key') and value:
            # Hide API keys for security
            display_value = f"{'*' * (len(str(value)) - 4)}{str(value)[-4:]}"
        else:
            display_value = str(value)

        table.add_row(
            key,
            display_value,
            descriptions.get(key, "")
        )

    console.print(table)
    console.print(f"\n[dim]Config file location: {CONFIG_FILE}[/dim]")


@config_app.command("set")
def set_config(
    key: str = typer.Argument(..., help="Configuration key"),
    value: str = typer.Argument(..., help="Configuration value")
):
    """
    🔧 Set a configuration value
    """
    config = _load_config()

    if key not in DEFAULT_CONFIG:
        console.print(f"[red]Unknown configuration key: {key}[/red]")
        console.print(f"[yellow]Valid keys: {', '.join(DEFAULT_CONFIG.keys())}[/yellow]")
        raise typer.Exit(1)

    # Type conversion based on default value
    default_value = DEFAULT_CONFIG[key]
    try:
        if isinstance(default_value, bool):
            config[key] = value.lower() in ('true', '1', 'yes', 'on')
        elif isinstance(default_value, (int, float)):
            config[key] = type(default_value)(value)
        elif isinstance(default_value, list):
            config[key] = [item.strip() for item in value.split(',')]
        else:
            config[key] = value
    except ValueError as e:
        console.print(f"[red]Invalid value for {key}: {e}[/red]")
        raise typer.Exit(1)

    if _save_config(config):
        console.print(f"[green]✓ Set {key} = {config[key]}[/green]")
    else:
        console.print("[red]Failed to save configuration[/red]")
        raise typer.Exit(1)


@config_app.command("reset")
def reset_config():
    """
    🔄 Reset configuration to defaults
    """
    if CONFIG_FILE.exists():
        CONFIG_FILE.unlink()
        console.print("[green]✓ Configuration reset to defaults[/green]")
    else:
        console.print("[yellow]Configuration already at defaults[/yellow]")


@config_app.command("edit")
def edit_config():
    """
    📝 Open configuration file in default editor
    """
    config = _load_config()  # Ensure config file exists
    _save_config(config)

    console.print(f"[blue]Configuration file: {CONFIG_FILE}[/blue]")
    console.print("[yellow]Open this file in your preferred text editor to make changes[/yellow]")


@config_app.callback()
def config_callback():
    """Configuration management commands"""
    pass
